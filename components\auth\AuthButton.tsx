"use client";

import { ReactNode } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

interface AuthButtonProps {
  type?: "button" | "submit";
  variant?: "primary" | "secondary" | "outline" | "ghost";
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  children: ReactNode;
  loadingText?: string;
  className?: string;
}

export function AuthButton({
  type = "button",
  variant = "primary",
  isLoading = false,
  disabled = false,
  onClick,
  children,
  loadingText = "در حال پردازش...",
  className = "",
}: AuthButtonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case "primary":
        return "bg-moonstone hover:bg-moonstone/90 text-white border-moonstone";
      case "secondary":
        return "bg-platinum hover:bg-platinum/90 text-charcoal border-platinum";
      case "outline":
        return "bg-white hover:bg-powder-blue/10 text-powder-blue border-powder-blue";
      case "ghost":
        return "bg-transparent hover:bg-platinum/20 text-charcoal border-transparent";
      default:
        return "bg-moonstone hover:bg-moonstone/90 text-white border-moonstone";
    }
  };

  const baseClasses =
    "w-full font-medium border rounded-lg shadow-sm hover:shadow-md transition-all duration-200 h-12 text-base";
  const variantClasses = getVariantClasses();

  return (
    <Button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses} ${className}`}
      size="lg"
    >
      {isLoading ? (
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin auth-spinner"></div>
          {loadingText}
        </div>
      ) : (
        children
      )}
    </Button>
  );
}
