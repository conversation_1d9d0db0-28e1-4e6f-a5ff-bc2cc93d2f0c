import { toast } from "sonner";

const toastStyle = {
  border: "3px solid var(--border)",
  boxShadow: "6px 6px 0 var(--border)",
  fontFamily: '"Vazirmatn", var(--font-geist-sans), system-ui, sans-serif',
  fontWeight: "bold",
  borderRadius: "8px",
  padding: "16px 20px",
  fontSize: "14px",
};

export const authToast = {
  success: (message: string) => {
    toast.success(message, {
      position: "top-center",
      duration: 3000,
      style: {
        ...toastStyle,
        background: "var(--primary)",
        color: "var(--primary-foreground)",
      },
      className: "auth-toast",
    });
  },

  error: (message: string) => {
    toast.error(message, {
      position: "top-center",
      duration: 4000,
      style: {
        ...toastStyle,
        background: "var(--destructive)",
        color: "white",
      },
      className: "auth-toast",
    });
  },

  info: (message: string) => {
    toast.success(message, {
      position: "top-center",
      duration: 3000,
      style: {
        ...toastStyle,
        background: "var(--accent)",
        color: "var(--accent-foreground)",
      },
      className: "auth-toast",
    });
  },
};
