import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage in client-side
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("access_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Common response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

// Type for outfit objects from API
export interface OutfitItem {
  id: string;
  title: string;
  description: string;
  image_url: string;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    username: string;
    profile_photo?: string;
  };
  reactions_count: number;
  has_reacted: boolean;
  tags: string[];
}

export interface User {
  id: string;
  username: string;
  email?: string;
  profile_photo?: string;
  bio?: string;
  followers_count: number;
  following_count: number;
  is_following?: boolean;
}

// Non-auth API calls

// User Profile API calls
export const profileApi = {
  getProfile: async () => {
    try {
      const response = await api.get<ApiResponse<User>>("/profile");
      return response.data;
    } catch (error: unknown) {
      console.error("Get profile error:", error);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        // Handle unauthorized by redirecting to login
        if (typeof window !== "undefined") {
          window.location.href = "/auth";
        }
      }

      // Return mock profile data for development
      return {
        success: true,
        data: {
          id: "1",
          username: "stylish_person",
          email: "<EMAIL>",
          bio: "Fashion enthusiast ✨ Sharing my daily outfits and style inspiration. Based in New York. #fashionlover #styleblogger",
          profile_photo: "/images/1.jpg",
          followers_count: 120,
          following_count: 85,
        },
      };
    }
  },

  updateProfile: async (profileData: Partial<User>) => {
    try {
      const response = await api.put<ApiResponse<User>>(
        "/profile",
        profileData
      );
      return response.data;
    } catch (error: unknown) {
      console.error("Update profile error:", error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در به‌روزرسانی پروفایل",
      };
    }
  },

  uploadProfilePhoto: async (file: File) => {
    try {
      const formData = new FormData();
      formData.append("photo", file);

      const response = await api.post<ApiResponse<{ profile_photo: string }>>(
        "/profile/photo",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error: unknown) {
      console.error("Upload profile photo error:", error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در بارگذاری تصویر پروفایل",
      };
    }
  },
};

// Outfits API
export const outfitsApi = {
  // Get all outfits
  getOutfits: async (page = 1, limit = 10) => {
    try {
      const response = await api.get<ApiResponse<OutfitItem[]>>(
        `/outfits?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error("Get outfits error:", error);
      return {
        success: false,
        data: [],
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت استایل‌ها",
      };
    }
  },

  // Get a specific outfit
  getOutfit: async (id: string) => {
    try {
      const response = await api.get<ApiResponse<OutfitItem>>(`/outfits/${id}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`Get outfit ${id} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت جزئیات استایل",
      };
    }
  },

  // Create a new outfit
  createOutfit: async (formData: FormData) => {
    try {
      const response = await api.post<ApiResponse<OutfitItem>>(
        "/outfits",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error: unknown) {
      console.error("Create outfit error:", error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در ایجاد استایل جدید",
      };
    }
  },

  // Update an outfit
  updateOutfit: async (id: string, data: Partial<OutfitItem>) => {
    try {
      const response = await api.put<ApiResponse<OutfitItem>>(
        `/outfits/${id}`,
        data
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Update outfit ${id} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در به‌روزرسانی استایل",
      };
    }
  },

  // Delete an outfit
  deleteOutfit: async (id: string) => {
    try {
      const response = await api.delete<ApiResponse<null>>(`/outfits/${id}`);
      return response.data;
    } catch (error: unknown) {
      console.error(`Delete outfit ${id} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در حذف استایل",
      };
    }
  },

  // React to an outfit (like/unlike)
  reactToOutfit: async (id: string) => {
    try {
      const response = await api.post<ApiResponse<{ has_reacted: boolean }>>(
        `/outfits/${id}/react`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`React to outfit ${id} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در ثبت واکنش به استایل",
      };
    }
  },

  // Search outfits
  searchOutfits: async (query: string, page = 1, limit = 10) => {
    try {
      const response = await api.get<ApiResponse<OutfitItem[]>>(
        `/outfits/search?query=${encodeURIComponent(
          query
        )}&page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error("Search outfits error:", error);
      return {
        success: false,
        data: [],
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در جستجوی استایل‌ها",
      };
    }
  },
};

// social API
export const socialApi = {
  // Follow a user
  followUser: async (userId: string) => {
    try {
      const response = await api.post<ApiResponse<{ is_following: boolean }>>(
        `/users/${userId}/follow`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Follow user ${userId} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دنبال کردن کاربر",
      };
    }
  },

  // Unfollow a user
  unfollowUser: async (userId: string) => {
    try {
      const response = await api.delete<ApiResponse<{ is_following: boolean }>>(
        `/users/${userId}/follow`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Unfollow user ${userId} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در لغو دنبال کردن کاربر",
      };
    }
  },

  // Get user followers
  getUserFollowers: async (userId: string, page = 1, limit = 20) => {
    try {
      const response = await api.get<ApiResponse<User[]>>(
        `/users/${userId}/followers?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Get user ${userId} followers error:`, error);
      return {
        success: false,
        data: [],
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت دنبال‌کنندگان",
      };
    }
  },

  // Get user following
  getUserFollowing: async (userId: string, page = 1, limit = 20) => {
    try {
      const response = await api.get<ApiResponse<User[]>>(
        `/users/${userId}/following?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Get user ${userId} following error:`, error);
      return {
        success: false,
        data: [],
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت دنبال‌شوندگان",
      };
    }
  },

  // Get user profile
  getUserProfile: async (username: string) => {
    try {
      const response = await api.get<ApiResponse<User>>(
        `/users/profile/${username}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Get user profile ${username} error:`, error);
      return {
        success: false,
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت پروفایل کاربر",
      };
    }
  },

  // Get user outfits
  getUserOutfits: async (userId: string, page = 1, limit = 10) => {
    try {
      const response = await api.get<ApiResponse<OutfitItem[]>>(
        `/users/${userId}/outfits?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error: unknown) {
      console.error(`Get user ${userId} outfits error:`, error);
      return {
        success: false,
        data: [],
        message:
          axios.isAxiosError(error) && error.response?.data?.message
            ? error.response.data.message
            : "خطا در دریافت استایل‌های کاربر",
      };
    }
  },
};

export default api;
