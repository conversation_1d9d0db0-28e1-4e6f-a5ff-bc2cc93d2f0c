"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { FASHION_IMAGES } from "@/lib/constants";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import {
  Heart,
  MessageCircle,
  Share2,
  Bookmark,
  Send,
  ArrowLeft,
  ShoppingBag,
  Tag,
  ExternalLink,
} from "lucide-react";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";

// Type for a comment
interface Comment {
  id: string;
  username: string;
  avatar: string;
  text: string;
  timestamp: string;
  likes: number;
}

// Type for an outfit item
interface OutfitItem {
  id: string;
  name: string;
  brand: string;
  price: number;
  currency: string;
  image: string;
  shopUrl: string;
  bodyPart: string;
}

// Type for estimated price
interface EstimatedPrice {
  amount: number;
  currency: string;
  range: string;
}

interface PostData {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  likes: number;
  comments: number;
  saves: number;
  timestamp: string;
  tags: string[];
  estimatedPrice?: EstimatedPrice;
  outfitItems?: OutfitItem[];
  user: {
    id: string;
    username: string;
    avatarUrl: string;
  };
}

export default function PostDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  const { isAuthenticated, userData } = useAuthContext();

  // State for post data - Now using mock data directly
  const [loading] = useState(false); // No loading needed for mock data

  // Extract column and image index from the id (format: "column-index")
  const [column, indexStr] = id.split("-");
  const index = parseInt(indexStr);

  // Get image from the FASHION_IMAGES array
  const imageIndex = parseInt(column) * 5 + index;
  const imageSrc = FASHION_IMAGES[imageIndex] || FASHION_IMAGES[0];

  // Mock post object - we'll manage likes/comments count directly
  const [post, setPost] = useState<PostData>({
    id,
    title: "استایل تابستانی با کت جین",
    description:
      "این استایل روزمره ترکیبی از راحتی و شیک‌پوشی برای یک روز تابستانی عالی است. کت جین سبک یک لایه‌بندی فوق‌العاده برای زمانی است که دما در شب کاهش می‌یابد.",
    imageSrc: imageSrc, // Use the derived image source
    likes: 432,
    comments: 3, // Initial comment count
    saves: 89,
    timestamp: "3 روز پیش",
    tags: ["تابستان", "جین", "روزمره", "استایل خیابانی"],
    // Example Outfit Items (can be expanded or kept simple)
    outfitItems: [
      {
        id: "item2",
        name: "تیشرت سفید ساده",
        brand: "زارا",
        price: 350000,
        currency: "تومان",
        image: "/images/items/shirt.jpg",
        shopUrl: "#",
        bodyPart: "upper",
      },
      {
        id: "item3",
        name: "شلوار جین آبی تیره",
        brand: "لی‌وایز",
        price: 890000,
        currency: "تومان",
        image: "/images/items/pants.jpg",
        shopUrl: "#",
        bodyPart: "lower",
      },
      {
        id: "item4",
        name: "کفش اسنیکرز سفید",
        brand: "نایک",
        price: 1350000,
        currency: "تومان",
        image: "/images/items/shoes.jpg",
        shopUrl: "#",
        bodyPart: "feet",
      },
      {
        id: "item5",
        name: "ساعت مچی چرمی",
        brand: "دنیل ولینگتون",
        price: 2200000,
        currency: "تومان",
        image: "/images/items/watch.jpg",
        shopUrl: "#",
        bodyPart: "accessory",
      },
    ],
    // Example Estimated Price
    estimatedPrice: {
      amount: 5990000,
      currency: "تومان",
      range: "۵٫۵۰۰٫۰۰۰ - ۶٫۵۰۰٫۰۰۰",
    },
    user: {
      id: "1",
      username: "فرد_شیک‌پوش",
      avatarUrl: "/images/1.jpg",
    },
  });

  // Mock comments data
  const [comments, setComments] = useState<Comment[]>([
    {
      id: "1",
      username: "عاشق_مد",
      avatar: "/images/2.jpg",
      text: "این استایل رو دوست دارم! از کجا این کت رو خریدی؟",
      timestamp: "2 days ago",
      likes: 12,
    },
    {
      id: "2",
      username: "استاد_استایل",
      avatar: "/images/3.jpg",
      text: "ترکیب رنگ‌ها فوق‌العاده‌ست! 👌",
      timestamp: "1 day ago",
      likes: 8,
    },
    {
      id: "3",
      username: "پیشرو_مد",
      avatar: "/images/4.jpg",
      text: "مدتیه دنبال این سبک می‌گردم. ممنون از الهام‌بخشیت!",
      timestamp: "12 hours ago",
      likes: 5,
    },
  ]);

  // State for the new comment
  const [newComment, setNewComment] = useState("");

  // State for interaction states
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  // Get recommended images (excluding current image)
  const recommendedImages = FASHION_IMAGES.filter(
    (img) => img !== imageSrc
  ).slice(0, 6);

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated
    if (!isAuthenticated) {
      toast.error("لطفا برای ارسال نظر وارد حساب کاربری خود شوید");
      router.push("/auth");
      return;
    }

    if (!newComment.trim()) return;

    // Simulate adding a comment locally
    const comment: Comment = {
      id: `comment-${Date.now()}`,
      username: userData?.username || "کاربر فعلی", // Use actual username if available
      avatar: userData?.profile_photo || "/images/default-avatar.png", // Use actual profile photo if available
      text: newComment,
      timestamp: "همین الان",
      likes: 0,
    };

    setComments([comment, ...comments]);
    setPost((prevPost) => ({ ...prevPost, comments: prevPost.comments + 1 })); // Update comment count
    setNewComment("");

    // Show a toast notification
    toast.success("نظر شما ثبت شد!");
  };

  const handleLike = () => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      toast.error("لطفا برای پسندیدن وارد حساب کاربری خود شوید");
      router.push("/auth");
      return;
    }

    // Simulate liking/unliking locally
    const newIsLiked = !isLiked;
    setIsLiked(newIsLiked);
    setPost((prevPost) => ({
      ...prevPost,
      likes: newIsLiked ? prevPost.likes + 1 : prevPost.likes - 1,
    }));
    toast.success(newIsLiked ? "به پسندها اضافه شد" : "از پسندها حذف شد");
  };

  const handleSave = () => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      toast.error("لطفا برای ذخیره کردن وارد حساب کاربری خود شوید");
      router.push("/auth");
      return;
    }

    // Simulate saving/unsaving locally
    const newIsSaved = !isSaved;
    setIsSaved(newIsSaved);
    setPost((prevPost) => ({
      ...prevPost,
      saves: newIsSaved ? prevPost.saves + 1 : prevPost.saves - 1,
    }));
    toast.success(newIsSaved ? "به ذخیره‌ها اضافه شد" : "از ذخیره‌ها حذف شد");
  };

  const handleShare = () => {
    // Share functionality - can be expanded later
    // For now, just copy the URL to clipboard
    if (navigator.clipboard) {
      navigator.clipboard.writeText(window.location.href);
      toast.success("لینک پست کپی شد!");
    }
  };

  // If we're in loading state, show a loading spinner
  if (loading) {
    return (
      <div className="container mx-auto flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 md:py-8 ">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          className="fun-hover border-0 shadow-none"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-6 w-6" />
        </Button>
        <h1 className="text-2xl font-bold mr-2">مشاهده استایل</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
        {/* Image Column */}
        <div className="relative">
          <div className="relative overflow-hidden rounded-lg border border-platinum bg-white shadow-md hover:shadow-lg transition-all">
            <Image
              src={post.imageSrc}
              alt={post.title}
              width={800}
              height={1000}
              className="w-full object-cover"
              style={{
                width: "100%",
                height: "auto",
                maxHeight: "80vh",
                objectFit: "cover",
              }}
            />
          </div>

          {/* Interaction buttons below the image - mobile only */}
          <div className="md:hidden flex justify-between mt-4">
            <div className="flex gap-4">
              <Button
                variant="outline"
                size="icon"
                onClick={handleLike}
                className={`${
                  isLiked ? "bg-moonstone text-white border-moonstone" : ""
                } transition-all shadow-sm hover:shadow-md`}
              >
                <Heart className={`h-5 w-5 ${isLiked ? "fill-current" : ""}`} />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  document.getElementById("comment-input")?.focus()
                }
                className="transition-all shadow-sm hover:shadow-md"
              >
                <MessageCircle className="h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleShare}
                className="transition-all shadow-sm hover:shadow-md"
              >
                <Share2 className="h-5 w-5" />
              </Button>
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={handleSave}
              className={`${
                isSaved
                  ? "bg-secondary text-secondary-foreground border-secondary"
                  : ""
              } transform  transition-transform border-2 shadow-[2px_2px_0_0] shadow-border`}
            >
              <Bookmark
                className={`h-5 w-5 ${isSaved ? "fill-current" : ""}`}
              />
            </Button>
          </div>
        </div>

        {/* Details Column */}
        <div className="flex flex-col h-full">
          {/* User info and post title */}
          <Card className="mb-6 relative overflow-visible">
            <CardContent className="pt-6">
              <div className="flex justify-between items-start w-full mb-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-border shadow-[3px_3px_0_0] shadow-border">
                    <AvatarImage src={post.user.avatarUrl} />
                    <AvatarFallback>
                      {post.user.username.slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <Link
                      href={`/profile/${post.user.id}`}
                      className="font-bold text-lg hover:underline block"
                    >
                      {post.user.username}
                    </Link>
                    <div className="text-muted-foreground text-sm">
                      {post.timestamp}
                    </div>
                  </div>
                </div>

                {/* Desktop interaction buttons */}
                <div className="hidden md:flex gap-3">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleLike}
                    className={`${
                      isLiked
                        ? "bg-primary text-primary-foreground border-primary"
                        : ""
                    } transform  transition-transform border-2 shadow-[2px_2px_0_0] shadow-border`}
                  >
                    <Heart
                      className={`h-5 w-5 ${isLiked ? "fill-current" : ""}`}
                    />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleSave}
                    className={`${
                      isSaved
                        ? "bg-secondary text-secondary-foreground border-secondary"
                        : ""
                    } transform  transition-transform border-2 shadow-[2px_2px_0_0] shadow-border`}
                  >
                    <Bookmark
                      className={`h-5 w-5 ${isSaved ? "fill-current" : ""}`}
                    />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleShare}
                    className="transform  transition-transform border-2 shadow-[2px_2px_0_0] shadow-border"
                  >
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <h2 className="text-2xl font-bold mb-2">{post.title}</h2>
              <p className="text-base">{post.description}</p>

              {/* Engagement counts */}
              <div className="flex gap-4 mt-4 text-sm">
                <div className="flex items-center gap-1">
                  <Heart className="h-4 w-4" />
                  <span>{post.likes} پسند</span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageCircle className="h-4 w-4" />
                  <span>{post.comments} نظر</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bookmark className="h-4 w-4" />
                  <span>{post.saves} ذخیره</span>
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mt-4">
                {post.tags.map((tag) => (
                  <Link
                    href={`/search?tag=${tag}`}
                    key={tag}
                    className="bg-primary/10 text-primary px-3 py-1 rounded-md text-sm hover:bg-primary/20 transition-colors border-2 border-primary shadow-[2px_2px_0_0] shadow-primary transform .5 hover:-translate-x-0.5"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Outfit Items */}
          {post.outfitItems && post.outfitItems.length > 0 && (
            <Card className="mb-6 pattern-stripes">
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-bold flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5" />
                    اجزای استایل
                  </h3>
                  {post.estimatedPrice && (
                    <div className="text-sm">
                      <span className="text-muted-foreground">
                        تخمین قیمت:{" "}
                      </span>
                      <span className="font-bold">
                        {post.estimatedPrice.range}{" "}
                        {post.estimatedPrice.currency}
                      </span>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {post.outfitItems.map((item) => {
                    // Define icon based on bodyPart
                    let BodyPartIcon = function DefaultIcon() {
                      return <Tag className="h-5 w-5" />;
                    };

                    // Set appropriate icon based on bodyPart
                    if (item.bodyPart === "upper") {
                      BodyPartIcon = function ShirtIcon() {
                        return (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
                          </svg>
                        );
                      };
                    } else if (item.bodyPart === "lower") {
                      BodyPartIcon = function PantsIcon() {
                        return (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M6 2v20M18 2v20M6 7h12M6 12h12" />
                          </svg>
                        );
                      };
                    } else if (item.bodyPart === "feet") {
                      BodyPartIcon = function ShoeIcon() {
                        return (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M2 15h10c2 0 3 .6 3 2s-1 2-3 2H5" />
                            <path d="M4 10h14a4 4 0 0 1 4 4v1a1 1 0 0 1-1 1H5" />
                            <path d="M4 7h13" />
                          </svg>
                        );
                      };
                    } else if (item.bodyPart === "accessory") {
                      BodyPartIcon = function WatchIcon() {
                        return (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <circle cx="12" cy="12" r="6" />
                            <path d="M12 10v2l1.5 1" />
                            <path d="M16.51 17.35 16 22H8l-.51-4.65" />
                            <path d="M8.51 6.65 9 2h6l.51 4.65" />
                          </svg>
                        );
                      };
                    }

                    return (
                      <div
                        key={item.id}
                        className="flex items-center gap-3 p-3 bg-card rounded-md border-2 border-border shadow-[3px_3px_0_0] shadow-border transform .5 hover:-translate-x-0.5 transition-transform"
                      >
                        <div className="flex-shrink-0 w-12 h-12 bg-accent/10 rounded-md flex items-center justify-center">
                          <BodyPartIcon />
                        </div>
                        <div className="flex-grow">
                          <h4 className="font-bold">{item.name}</h4>
                          <div className="flex justify-between items-center">
                            <div className="text-sm">
                              <span className="text-muted-foreground">
                                {item.brand}
                              </span>
                            </div>
                            <div className="text-sm font-semibold">
                              {item.price.toLocaleString()} {item.currency}
                            </div>
                          </div>
                        </div>
                        <a
                          href={item.shopUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex-shrink-0"
                        >
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 fun-hover"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </a>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comments Section */}
          <Card className="flex-grow">
            <CardContent className="pt-6">
              <h3 className="text-lg font-bold flex items-center gap-2 mb-4">
                <MessageCircle className="h-5 w-5" />
                نظرات ({post.comments})
              </h3>

              {/* Comment Form */}
              <form
                onSubmit={handleCommentSubmit}
                className="flex gap-3 mb-6"
                dir="rtl"
              >
                <Input
                  id="comment-input"
                  placeholder="نظر خود را بنویسید..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="flex-grow border-2 border-border shadow-[2px_2px_0_0] shadow-border"
                />
                <Button
                  type="submit"
                  className="flex-shrink-0 transform  transition-transform bg-[#458588] hover:bg-[#83a598] text-white border-[#3c3836] border-2 shadow-[3px_3px_0_0] shadow-[#3c3836]"
                >
                  <Send className="h-4 w-4 ml-2" />
                  ارسال
                </Button>
              </form>

              {/* Comments List */}
              <div className="space-y-4">
                {comments.length > 0 ? (
                  comments.map((comment) => (
                    <div
                      key={comment.id}
                      className="flex gap-3 p-4 rounded-md border-2 border-border bg-background shadow-[4px_4px_0_0] shadow-border transform .5 hover:-translate-x-0.5 transition-transform"
                    >
                      <Avatar className="ring-1 ring-border shadow-[2px_2px_0_0] shadow-border">
                        <AvatarImage src={comment.avatar} />
                        <AvatarFallback>
                          {comment.username.slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-grow">
                        <div className="flex justify-between items-start">
                          <span className="font-semibold">
                            {comment.username}
                          </span>
                          <span className="text-xs text-muted-foreground px-2 py-1 bg-muted rounded-md">
                            {comment.timestamp}
                          </span>
                        </div>
                        <p className="text-sm mt-2 mb-3">{comment.text}</p>
                        <div className="flex gap-4">
                          <button className="text-xs flex items-center gap-1 hover:opacity-80 transition-opacity px-2 py-1 rounded-md bg-muted border border-border">
                            <Heart
                              className={`h-3 w-3 ${
                                comment.likes > 0 ? "fill-primary" : ""
                              }`}
                            />
                            {comment.likes} پسند
                          </button>
                          <button className="text-xs flex items-center gap-1 hover:opacity-80 transition-opacity px-2 py-1 rounded-md bg-muted border border-border">
                            <MessageCircle className="h-3 w-3" />
                            پاسخ
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-muted-foreground p-6 border-2 border-dashed border-border rounded-md">
                    هنوز نظری ثبت نشده است. اولین نفری باشید که نظر می‌دهید!
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recommended Posts */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-6 relative inline-block">
          استایل‌های مشابه
          <div className="absolute -bottom-1 left-0 right-0 h-2 bg-primary/30 -z-10"></div>
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {recommendedImages.map((image, idx) => (
            <Link
              href={`/pin/0-${idx}`}
              key={idx}
              className="block overflow-hidden rounded-md border-2 border-border shadow-[4px_4px_0_0] shadow-border transform  hover:-translate-x-1 hover:shadow-[6px_6px_0_0] transition-all"
            >
              <Image
                src={image}
                alt={`Recommended style ${idx + 1}`}
                width={200}
                height={300}
                className="w-full h-auto object-cover aspect-[3/4]"
              />
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
