"use client";

import React, { createContext, useContext, ReactNode } from "react";
import useAuth from "../hooks/useAuth";

interface UserData {
  id?: string | number;
  username?: string;
  email?: string;
  bio?: string;
  avatarUrl?: string;
  profile_photo?: string;
  first_name?: string | null;
  last_name?: string | null;
  is_staff?: boolean;
  is_active?: boolean;
  date_joined?: number;
  gender?: string | null;
  phone_number?: string | null;
  followers_count?: number;
  following_count?: number;
  [key: string]: unknown;
}

interface AuthContextType {
  isAuthenticated: boolean;
  hasTempTokens: boolean;
  userData: UserData | null;
  loading: boolean;
  logout: () => void;
  checkAuth: () => void;
  setAuthTokens: (
    accessToken: string,
    refreshToken: string,
    userData?: UserData
  ) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const auth = useAuth();

  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;
};

export default AuthProvider;
