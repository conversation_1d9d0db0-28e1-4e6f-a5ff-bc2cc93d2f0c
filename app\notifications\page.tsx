"use client";

import { <PERSON>, <PERSON>, MessageCircle, User, UserPlus } from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Notification {
  id: number;
  type: "like" | "comment" | "follow" | "mention";
  username: string;
  time: string;
  read: boolean;
  imageUrl?: string;
}

// Mock notification data
const notifications: Notification[] = [
  {
    id: 1,
    type: "like",
    username: "fashion_lover",
    time: "2 minutes ago",
    read: false,
    imageUrl: "/images/4.jpg",
  },
  {
    id: 2,
    type: "comment",
    username: "style_guru",
    time: "1 hour ago",
    read: false,
    imageUrl: "/images/7.jpg",
  },
  {
    id: 3,
    type: "follow",
    username: "trendsetter",
    time: "3 hours ago",
    read: true,
  },
  {
    id: 4,
    type: "like",
    username: "outfit_inspiration",
    time: "5 hours ago",
    read: true,
    imageUrl: "/images/11.jpg",
  },
  {
    id: 5,
    type: "mention",
    username: "fashion_forward",
    time: "1 day ago",
    read: true,
    imageUrl: "/images/15.jpg",
  },
];

// Helper to get icon based on notification type
function getNotificationIcon(type: Notification["type"]) {
  switch (type) {
    case "like":
      return <Heart className="h-5 w-5 text-red-500" />;
    case "comment":
      return <MessageCircle className="h-5 w-5 text-blue-500" />;
    case "follow":
      return <UserPlus className="h-5 w-5 text-green-500" />;
    case "mention":
      return <User className="h-5 w-5 text-purple-500" />;
    default:
      return <Bell className="h-5 w-5" />;
  }
}

// Helper to get notification text in Persian
function getNotificationText(notification: Notification) {
  switch (notification.type) {
    case "like":
      return "استایل شما را پسندید";
    case "comment":
      return "روی استایل شما نظر داد";
    case "follow":
      return "شما را دنبال کرد";
    case "mention":
      return "شما را در نظر خود منشن کرد";
    default:
      return "با محتوای شما تعامل داشت";
  }
}

// Persian time labels
function getPersianTime(time: string): string {
  if (time.includes("minutes")) return time.replace("minutes ago", "دقیقه پیش");
  if (time.includes("hour")) return time.replace("hour ago", "ساعت پیش");
  if (time.includes("hours")) return time.replace("hours ago", "ساعت پیش");
  if (time.includes("day")) return time.replace("day ago", "روز پیش");
  return time;
}

export default function NotificationsPage() {
  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <div
      className="container mx-auto mt-2 px-4 py-6 md:py-8 max-w-3xl"
      dir="rtl"
    >
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">اعلان‌ها</h1>
        <Badge variant="secondary" className="text-xs lg:text-sm">
          {unreadCount} اعلان خوانده نشده
        </Badge>
      </div>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="divide-y">
            {notifications.map((notification, index) => (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`p-4 lg:py-6 flex items-start gap-3 ${
                  !notification.read
                    ? "bg-moonstone/5 border-l-2 border-moonstone/20"
                    : "hover:bg-platinum/30 transition-colors"
                }`}
              >
                <div className="rounded-full p-2 bg-white border border-platinum shadow-sm">
                  {getNotificationIcon(notification.type)}
                </div>

                <div className="flex-grow">
                  <p className="text-sm lg:text-base font-medium">
                    <span className="font-bold text-moonstone">
                      @{notification.username}
                    </span>{" "}
                    {getNotificationText(notification)}
                  </p>
                  <p className="text-xs lg:text-sm text-muted-foreground mt-1">
                    {getPersianTime(notification.time)}
                  </p>
                </div>

                {notification.imageUrl && (
                  <Avatar className="h-12 w-12 lg:h-14 lg:w-14 rounded-md">
                    <AvatarImage
                      src={notification.imageUrl}
                      alt="تصویر استایل"
                      className="object-cover"
                    />
                    <AvatarFallback>👕</AvatarFallback>
                  </Avatar>
                )}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
