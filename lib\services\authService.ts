import axios from "axios";
import { set<PERSON><PERSON>ie, deleteCookie } from "cookies-next";

// Define API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Validate API URL
if (!API_BASE_URL) {
  console.error("API URL not configured in environment variables");
  // We'll still proceed but log the error as this is crucial for the app
}

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Response types
export interface AuthResponse {
  success: boolean;
  access_token?: string;
  refresh_token?: string;
  user_data?: UserData;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
}

export interface UserData {
  id?: string | number;
  username?: string;
  email?: string;
  bio?: string;
  avatarUrl?: string;
  profile_photo?: string;
  first_name?: string | null;
  last_name?: string | null;
  is_staff?: boolean;
  is_active?: boolean;
  date_joined?: number;
  gender?: string | null;
  phone_number?: string | null;
  followers_count?: number;
  following_count?: number;
  [key: string]: unknown;
}

export interface RegisterResponse {
  success: boolean;
  message?: string;
  tokens?: {
    refresh: string;
    access: string;
  };
  user_data?: UserData;
  access_token?: string;
  refresh_token?: string;
}

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage in client-side
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("access_token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Helper functions
const setAuthData = (
  accessToken: string,
  refreshToken: string,
  userData?: UserData
) => {
  if (typeof window === "undefined") return;

  // Save tokens to localStorage
  localStorage.setItem("access_token", accessToken);
  localStorage.setItem("refresh_token", refreshToken);

  // Save user data if provided
  if (userData) {
    localStorage.setItem("user_data", JSON.stringify(userData));
  }

  // Save to cookies as well for better security
  const oneMonthInSeconds = 30 * 24 * 60 * 60;
  setCookie("access_token", accessToken, {
    maxAge: oneMonthInSeconds,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });
  setCookie("refresh_token", refreshToken, {
    maxAge: oneMonthInSeconds,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // Set Authorization header for future requests
  api.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
};

// Function to set temporary tokens for unverified users
const setTempAuthData = (
  accessToken: string,
  refreshToken: string,
  userData?: UserData
) => {
  if (typeof window === "undefined") return;

  // Save temporary tokens to localStorage
  localStorage.setItem("temp_access_token", accessToken);
  localStorage.setItem("temp_refresh_token", refreshToken);

  // Save user data if provided
  if (userData) {
    localStorage.setItem("temp_user_data", JSON.stringify(userData));
  }
};

// Convert temporary tokens to permanent ones after verification
const convertTempTokensToPermanent = () => {
  if (typeof window === "undefined") return false;

  const tempAccessToken = localStorage.getItem("temp_access_token");
  const tempRefreshToken = localStorage.getItem("temp_refresh_token");
  const tempUserData = localStorage.getItem("temp_user_data");

  if (!tempAccessToken || !tempRefreshToken) return false;

  // Set permanent tokens
  localStorage.setItem("access_token", tempAccessToken);
  localStorage.setItem("refresh_token", tempRefreshToken);

  if (tempUserData) {
    localStorage.setItem("user_data", tempUserData);
  }

  // Set cookies
  const oneMonthInSeconds = 30 * 24 * 60 * 60;
  setCookie("access_token", tempAccessToken, {
    maxAge: oneMonthInSeconds,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });
  setCookie("refresh_token", tempRefreshToken, {
    maxAge: oneMonthInSeconds,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // Set Authorization header
  api.defaults.headers.common["Authorization"] = `Bearer ${tempAccessToken}`;

  // Clean up temporary tokens
  localStorage.removeItem("temp_access_token");
  localStorage.removeItem("temp_refresh_token");
  localStorage.removeItem("temp_user_data");

  return true;
};

const clearAuthData = () => {
  if (typeof window === "undefined") return;

  // Remove data from localStorage
  localStorage.removeItem("access_token");
  localStorage.removeItem("refresh_token");
  localStorage.removeItem("user_data");

  // Also clear any temporary tokens
  localStorage.removeItem("temp_access_token");
  localStorage.removeItem("temp_refresh_token");
  localStorage.removeItem("temp_user_data");

  // Remove cookies
  deleteCookie("access_token");
  deleteCookie("refresh_token");

  // Remove Authorization header
  delete api.defaults.headers.common["Authorization"];
};

// Auth API service
const authService = {
  // Login with username/email and password
  login: async (
    identifier: string,
    password: string
  ): Promise<AuthResponse> => {
    try {
      const response = await api.post<AuthResponse>("/users/login", {
        identifier,
        password,
      });

      if (
        response.data.success &&
        response.data.access_token &&
        response.data.refresh_token
      ) {
        setAuthData(
          response.data.access_token,
          response.data.refresh_token,
          response.data.user_data
        );
      }

      return response.data;
    } catch (error: unknown) {
      console.error("Login error:", error);
      return {
        success: false,
      };
    }
  },

  // Register new user
  register: async (
    email: string,
    username: string,
    password: string,
    confirm_password: string
  ): Promise<AuthResponse> => {
    try {
      const response = await api.post<RegisterResponse>("/users/register", {
        email,
        username,
        password,
        confirm_password,
      });

      // Check for tokens directly in the response (preferred format)
      if (response.data.access_token && response.data.refresh_token) {
        const accessToken = response.data.access_token;
        const refreshToken = response.data.refresh_token;
        const userData = response.data.user_data || { username, email };

        // Use temporary tokens for unverified users
        setTempAuthData(accessToken, refreshToken, userData);

        return {
          success: true,
          access_token: accessToken,
          refresh_token: refreshToken,
          user_data: userData,
        };
      }
      // For backwards compatibility, also check the nested format
      else if (response.data.success && response.data.tokens) {
        const accessToken = response.data.tokens.access;
        const refreshToken = response.data.tokens.refresh;
        const userData = response.data.user_data || { username, email };

        // Use temporary tokens for unverified users
        setTempAuthData(accessToken, refreshToken, userData);

        return {
          success: true,
          access_token: accessToken,
          refresh_token: refreshToken,
          user_data: userData,
        };
      }

      return { success: true };
    } catch (error: unknown) {
      console.error("Register error:", error);
      return {
        success: false,
      };
    }
  },

  // Verify email with code
  verifyCode: async (code: number): Promise<ApiResponse<unknown>> => {
    try {
      // Use temporary access token for verification
      const tempAccessToken = localStorage.getItem("temp_access_token");

      if (!tempAccessToken) {
        console.error("No temporary access token found for verification");
        return { success: false };
      }

      // Create a custom config with Authorization header
      const config = {
        headers: {
          Authorization: `Bearer ${tempAccessToken}`,
        },
      };

      // Send only the code in the request body
      const response = await api.post<ApiResponse<unknown>>(
        "/users/verify-code",
        { code },
        config
      );

      // If verification was successful, convert temporary tokens to permanent ones
      if (response.data.success) {
        convertTempTokensToPermanent();
      }

      return response.data;
    } catch (error: unknown) {
      console.error("Verify code error:", error);
      return {
        success: false,
      };
    }
  },

  // Resend verification code
  resendVerifyCode: async (): Promise<ApiResponse<unknown>> => {
    try {
      const tempAccessToken = localStorage.getItem("temp_access_token");

      if (!tempAccessToken) {
        console.error(
          "No temporary access token found for resending verification code"
        );
        return { success: false };
      }

      // Create a custom config with Authorization header
      const config = {
        headers: {
          Authorization: `Bearer ${tempAccessToken}`,
        },
      };

      // Use the /api/users/verify-code endpoint with GET method
      const response = await api.get<ApiResponse<unknown>>(
        "/users/verify-code",
        config
      );

      return response.data;
    } catch (error: unknown) {
      console.error("Resend verify code error:", error);
      return {
        success: false,
      };
    }
  },

  // Refresh access token using refresh token
  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    try {
      const response = await api.post<{
        access_token: string;
        refresh_token: string;
      }>("/users/refresh-token", {
        refresh_token: refreshToken,
      });

      if (response.data.access_token && response.data.refresh_token) {
        setAuthData(response.data.access_token, response.data.refresh_token);
      }

      return {
        success: true,
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
      };
    } catch (error: unknown) {
      console.error("Refresh token error:", error);

      // Token might be invalid, logout user
      authService.logout();

      return {
        success: false,
      };
    }
  },

  // Logout the user
  logout: () => {
    const refreshToken = localStorage.getItem("refresh_token");
    const accessToken = localStorage.getItem("access_token");

    // Call the server to invalidate the tokens if available
    if (refreshToken && accessToken) {
      api
        .post(
          "/users/logout",
          {
            access_token: accessToken,
            refresh_token: refreshToken,
          },
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        )
        .catch((error) => {
          console.error("Server logout error:", error);
        });
    }

    clearAuthData();
    return { success: true };
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    if (typeof window === "undefined") return false;
    return !!localStorage.getItem("access_token");
  },

  // Check if user has temporary tokens (registered but not verified)
  hasTempTokens: (): boolean => {
    if (typeof window === "undefined") return false;
    return !!localStorage.getItem("temp_access_token");
  },

  // Get current auth token
  getToken: (): string | null => {
    if (typeof window === "undefined") return null;
    return localStorage.getItem("access_token");
  },

  // Get user data from localStorage
  getUserData: (): UserData | null => {
    if (typeof window === "undefined") return null;

    const userDataStr = localStorage.getItem("user_data");
    if (!userDataStr) return null;

    try {
      return JSON.parse(userDataStr);
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  },

  // Request password reset
  requestPasswordReset: async (
    email: string
  ): Promise<{
    success: boolean;
    processId?: string;
  }> => {
    try {
      const response = await api.get<{
        success: boolean;
        message: string;
        process_id: string;
      }>(`/users/reset-password?email=${encodeURIComponent(email)}`);

      return {
        success: response.data.success,
        processId: response.data.process_id,
      };
    } catch (error: unknown) {
      console.error("Reset password request error:", error);
      return {
        success: false,
      };
    }
  },

  // Verify reset password code
  verifyResetCode: async (
    processId: string,
    code: number
  ): Promise<ApiResponse<unknown>> => {
    try {
      const response = await api.put<ApiResponse<unknown>>(
        "/users/reset-password",
        {
          process_id: processId,
          code,
        }
      );

      return response.data;
    } catch (error: unknown) {
      console.error("Verify reset code error:", error);
      return {
        success: false,
      };
    }
  },

  // Set new password after verification
  resetPassword: async (
    processId: string,
    newPassword: string,
    newPasswordConfirm: string
  ): Promise<ApiResponse<unknown>> => {
    try {
      const response = await api.post<ApiResponse<unknown>>(
        "/users/reset-password",
        {
          process_id: processId,
          new_password: newPassword,
          new_password_confirm: newPasswordConfirm,
        }
      );

      return response.data;
    } catch (error: unknown) {
      console.error("Reset password error:", error);
      return {
        success: false,
      };
    }
  },
};

export default authService;
