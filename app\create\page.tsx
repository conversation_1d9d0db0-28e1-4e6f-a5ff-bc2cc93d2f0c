"use client";

import { useState } from "react";
import {
  Upload,
  X,
  Plus,
  Tag,
  ExternalLink,
  ArrowRight,
  ArrowLeft,
} from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Interface for outfit item
interface OutfitItem {
  id: string;
  name: string;
  brand: string;
  price: string;
  currency: string;
  shopUrl: string;
  bodyPart: string;
  image?: File;
  imagePreview?: string;
}

export default function CreatePage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // New state for outfit items
  const [outfitItems, setOutfitItems] = useState<OutfitItem[]>([]);
  const [estimatedPrice, setEstimatedPrice] = useState("");
  const [estimatedPriceRange, setEstimatedPriceRange] = useState("");
  const [currency, setCurrency] = useState("تومان");

  const router = useRouter();
  const { isAuthenticated, loading } = useAuthContext();

  // Check if user is authenticated
  if (!loading && !isAuthenticated) {
    toast.error("برای ارسال پست باید وارد حساب کاربری خود شوید");
    router.push("/auth");
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error("حجم فایل باید کمتر از ۱۰ مگابایت باشد");
        return;
      }

      setSelectedFile(file);

      // Create a preview URL for the selected image
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleItemImageChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    itemId: string
  ) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 5MB for item images)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("حجم فایل آیتم باید کمتر از ۵ مگابایت باشد");
        return;
      }

      // Create a preview URL for the selected image
      const reader = new FileReader();
      reader.onloadend = () => {
        setOutfitItems((prev) =>
          prev.map((item) =>
            item.id === itemId
              ? { ...item, image: file, imagePreview: reader.result as string }
              : item
          )
        );
      };
      reader.readAsDataURL(file);
    }
  };

  const clearSelectedFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  const addOutfitItem = () => {
    const newItem: OutfitItem = {
      id: `item-${Date.now()}`,
      name: "",
      brand: "",
      price: "",
      currency: "تومان",
      shopUrl: "",
      bodyPart: "upper",
    };
    setOutfitItems([...outfitItems, newItem]);
  };

  const updateOutfitItem = (
    id: string,
    field: keyof OutfitItem,
    value: string
  ) => {
    setOutfitItems((prevItems) =>
      prevItems.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  const removeOutfitItem = (id: string) => {
    setOutfitItems((prevItems) => prevItems.filter((item) => item.id !== id));
  };

  // SVG components for each body part
  const getBodyPartIcon = (bodyPart: string) => {
    switch (bodyPart) {
      case "upper":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
          >
            <path d="M20.38 3.46L16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
          </svg>
        );
      case "lower":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
          >
            <path d="M6 2v20l6-4l6 4V2z" />
          </svg>
        );
      case "feet":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
          >
            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
            <path d="M4 6h5.426a1 1 0 0 1 .863 .496l1.064 1.823a3 3 0 0 0 1.896 1.407l4.677 1.114a4 4 0 0 1 3.074 3.89v2.27a1 1 0 0 1 -1 1h-16a1 1 0 0 1 -1 -1v-10a1 1 0 0 1 1 -1z" />
            <path d="M14 13l1 -2" />
            <path d="M8 18v-1a4 4 0 0 0 -4 -4h-1" />
            <path d="M10 12l1.5 -3" />
          </svg>
        );
      case "accessory":
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-moonstone"
          >
            <circle cx="12" cy="12" r="7" />
            <polyline points="12 9 12 12 13.5 13.5" />
            <path d="M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83" />
          </svg>
        );
      default:
        return <Tag className="text-moonstone" />;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      toast.error("لطفاً یک تصویر انتخاب کنید");
      return;
    }

    if (!title.trim()) {
      toast.error("لطفاً یک عنوان وارد کنید");
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call with a delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Log the collected data (in a real app, this would be sent to an API)
      console.log({
        title,
        description,
        tags,
        outfitItems,
        estimatedPrice,
        estimatedPriceRange,
        currency,
      });

      toast.success("استایل شما با موفقیت به اشتراک گذاشته شد!");

      // Reset form
      setSelectedFile(null);
      setPreviewUrl(null);
      setTitle("");
      setDescription("");
      setTags("");
      setOutfitItems([]);
      setEstimatedPrice("");
      setEstimatedPriceRange("");
      setCurrentStep(1);

      // Redirect to profile page
      router.push("/profile");
    } catch (error) {
      console.error("Error creating post:", error);
      toast.error("خطا در آپلود پست. لطفا دوباره تلاش کنید.");
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate total from outfit items
  const calculateTotal = () => {
    const total = outfitItems.reduce((sum, item) => {
      const price = parseFloat(item.price) || 0;
      return sum + price;
    }, 0);
    return total.toLocaleString();
  };

  // Validation function that always shows a toast when no items
  const validateStep1WithToast = () => {
    if (outfitItems.length === 0) {
      toast.error(
        "لطفاً ابتدا حداقل یک آیتم اضافه کنید سپس به مرحله بعد بروید",
        {
          duration: 3000,
          position: "top-center",
        }
      );
      return false;
    }

    // Check if all items have required fields
    let hasIncompleteItem = false;
    const incompleteFields = [];

    for (const item of outfitItems) {
      if (!item.name.trim()) incompleteFields.push("نام");
      if (!item.brand.trim()) incompleteFields.push("برند");
      if (!item.price.trim()) incompleteFields.push("قیمت");

      if (!item.name.trim() || !item.brand.trim() || !item.price.trim()) {
        hasIncompleteItem = true;
        break;
      }
    }

    if (hasIncompleteItem) {
      toast.error(
        `لطفاً تمام فیلدهای ضروری (${incompleteFields.join(
          "، "
        )}) آیتم‌ها را کامل کنید`,
        {
          duration: 3000,
          position: "top-center",
        }
      );
      return false;
    }

    return true;
  };

  // Simple validation check without toast messages - for button disabled state
  const canProceedToStep2 = () => {
    if (outfitItems.length === 0) {
      return false;
    }

    // Check if all items have required fields
    for (const item of outfitItems) {
      if (!item.name.trim() || !item.brand.trim() || !item.price.trim()) {
        return false;
      }
    }

    return true;
  };

  // Move to a specific step with validation
  const goToStep = (step: number) => {
    // Validate before going to step 2
    if (step === 2 && currentStep === 1) {
      if (!validateStep1WithToast()) {
        return;
      }
    }
    setCurrentStep(step);
  };

  // Render step 1 - Outfit Items
  const renderStep1 = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold mb-4">افزودن آیتم‌های استایل</h2>

        <div className="space-y-4">
          {outfitItems.map((item, index) => (
            <Card key={item.id} className="bg-platinum/20 overflow-hidden">
              <CardContent className="p-4 lg:px-6">
                <div className="flex items-center justify-between mb-8 border-b-2 pb-2">
                  <div className="flex items-center">
                    {getBodyPartIcon(item.bodyPart)}
                    <h4 className="font-medium mr-2">آیتم {index + 1}</h4>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeOutfitItem(item.id)}
                    disabled={isLoading}
                  >
                    <X size={22} className="text-destructive" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <Label
                        htmlFor={`item-name-${item.id}`}
                        className="mb-2 block"
                      >
                        نام آیتم
                      </Label>
                      <Input
                        id={`item-name-${item.id}`}
                        placeholder="نام آیتم"
                        value={item.name}
                        onChange={(e) =>
                          updateOutfitItem(item.id, "name", e.target.value)
                        }
                        disabled={isLoading}
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor={`item-brand-${item.id}`}
                        className="mb-2 block"
                      >
                        برند
                      </Label>
                      <Input
                        id={`item-brand-${item.id}`}
                        placeholder="برند آیتم"
                        value={item.brand}
                        onChange={(e) =>
                          updateOutfitItem(item.id, "brand", e.target.value)
                        }
                        disabled={isLoading}
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor={`item-price-${item.id}`}
                        className="mb-2 block"
                      >
                        قیمت
                      </Label>
                      <div className="flex">
                        <Input
                          id={`item-price-${item.id}`}
                          placeholder="قیمت آیتم"
                          value={item.price}
                          onChange={(e) =>
                            updateOutfitItem(
                              item.id,
                              "price",
                              e.target.value.replace(/\D/g, "")
                            )
                          }
                          className="rounded-l-none"
                          disabled={isLoading}
                        />
                        <Select
                          value={item.currency}
                          onValueChange={(value: string) =>
                            updateOutfitItem(item.id, "currency", value)
                          }
                          disabled={isLoading}
                        >
                          <SelectTrigger className="w-24 rounded-r-none">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent dir="rtl" className="text-right">
                            <SelectItem value="تومان">تومان</SelectItem>
                            <SelectItem value="دلار">دلار</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor={`item-type-${item.id}`}
                        className="mb-2 block"
                      >
                        نوع آیتم
                      </Label>
                      <Select
                        value={item.bodyPart}
                        onValueChange={(value: string) =>
                          updateOutfitItem(item.id, "bodyPart", value)
                        }
                        disabled={isLoading}
                      >
                        <SelectTrigger className="flex justify-between">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent dir="rtl" className="text-right">
                          <SelectItem value="upper">بالاتنه</SelectItem>
                          <SelectItem value="lower">پایین‌تنه</SelectItem>
                          <SelectItem value="feet">کفش</SelectItem>
                          <SelectItem value="accessory">اکسسوری</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label
                        htmlFor={`item-link-${item.id}`}
                        className="mb-2 block"
                      >
                        لینک خرید
                      </Label>
                      <div className="flex relative">
                        <ExternalLink
                          size={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                        />
                        <Input
                          id={`item-link-${item.id}`}
                          placeholder="لینک فروشگاه"
                          value={item.shopUrl}
                          onChange={(e) =>
                            updateOutfitItem(item.id, "shopUrl", e.target.value)
                          }
                          disabled={isLoading}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="mb-2 block">تصویر آیتم</Label>
                    {!item.imagePreview ? (
                      <div className="border border-dashed border-muted rounded-lg p-4 text-center h-[200px] flex flex-col items-center justify-center">
                        <input
                          type="file"
                          id={`item-image-${item.id}`}
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => handleItemImageChange(e, item.id)}
                          disabled={isLoading}
                        />
                        <label
                          htmlFor={`item-image-${item.id}`}
                          className="cursor-pointer flex flex-col items-center justify-center gap-2 w-full h-full"
                        >
                          <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                          <span className="text-sm font-medium text-muted-foreground">
                            آپلود تصویر آیتم
                          </span>
                          <span className="text-xs text-muted-foreground mt-1">
                            کلیک کنید یا تصویر را اینجا رها کنید
                          </span>
                        </label>
                      </div>
                    ) : (
                      <div className="relative h-[200px]">
                        <Image
                          src={item.imagePreview}
                          alt={item.name}
                          fill
                          className="rounded-lg object-contain bg-accent/10"
                        />
                        <button
                          type="button"
                          className="absolute top-2 left-2 bg-black/50 text-white p-1 rounded-full"
                          onClick={() =>
                            setOutfitItems((prev) =>
                              prev.map((i) =>
                                i.id === item.id
                                  ? {
                                      ...i,
                                      image: undefined,
                                      imagePreview: undefined,
                                    }
                                  : i
                              )
                            )
                          }
                          disabled={isLoading}
                        >
                          <X size={16} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addOutfitItem}
            disabled={isLoading}
            className="mt-4 w-full border-dashed hover:bg-accent/30 transition-colors duration-300 py-6"
          >
            افزودن آیتم جدید
            <Plus size={16} className="mr-auto" />
          </Button>
        </div>

        <div className="flex justify-end mt-6">
          <Button
            onClick={() => goToStep(2)}
            className="bg-primary text-white"
            disabled={!canProceedToStep2()}
          >
            مرحله بعد
            <ArrowLeft className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  // Render step 2 - Post Details
  const renderStep2 = () => {
    return (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold mb-4">اطلاعات پست</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left column - Main post details */}
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title" className="mb-2 block">
                عنوان
              </Label>
              <Input
                id="title"
                placeholder="استایل شما چه نام دارد؟"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="mb-2 block">
                توضیحات
              </Label>
              <Textarea
                id="description"
                placeholder="درباره استایل خود به ما بگویید..."
                className="min-h-[120px]"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags" className="mb-2 block">
                برچسب‌ها (با کاما جدا کنید)
              </Label>
              <Input
                id="tags"
                placeholder="مثال: تابستانی، رسمی، اسپرت"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                disabled={isLoading}
              />
            </div>

            {/* Estimated Price Section */}
            <Card className="bg-accent/5">
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <Tag className="ml-2 text-primary" />
                  قیمت تخمینی استایل
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                  <div>
                    <Label htmlFor="estimatedPrice" className="mb-2 block">
                      قیمت تخمینی
                    </Label>
                    <div className="flex">
                      <Input
                        id="estimatedPrice"
                        placeholder="مبلغ"
                        value={estimatedPrice}
                        onChange={(e) =>
                          setEstimatedPrice(e.target.value.replace(/\D/g, ""))
                        }
                        className="rounded-l-none"
                        disabled={isLoading}
                      />
                      <Select
                        value={currency}
                        onValueChange={setCurrency}
                        disabled={isLoading}
                      >
                        <SelectTrigger
                          className="w-24 rounded-r-none"
                          dir="ltr"
                        >
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent dir="rtl" className="text-right">
                          <SelectItem value="تومان">تومان</SelectItem>
                          <SelectItem value="دلار">دلار</SelectItem>
                          <SelectItem value="یورو">یورو</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="priceRange" className="mb-2 block">
                      بازه قیمت
                    </Label>
                    <Input
                      id="priceRange"
                      placeholder="مثال: ۵٫۵۰۰٫۰۰۰ - ۶٫۵۰۰٫۰۰۰"
                      value={estimatedPriceRange}
                      onChange={(e) => setEstimatedPriceRange(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                </div>
                {outfitItems.length > 0 && (
                  <div className="mt-3 text-sm text-muted-foreground">
                    مجموع قیمت آیتم‌ها: {calculateTotal()} {currency}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right column - Image upload and items preview */}
          <div className="space-y-6">
            <div className="space-y-2">
              <Label className="block mb-2">آپلود تصویر استایل</Label>

              {!previewUrl ? (
                <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                  <input
                    type="file"
                    id="file-upload"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                    disabled={isLoading}
                  />
                  <label
                    htmlFor="file-upload"
                    className="cursor-pointer flex flex-col items-center justify-center gap-3 py-4"
                  >
                    <Upload className="h-12 w-12 text-muted-foreground" />
                    <span className="text-base font-medium text-muted-foreground">
                      برای آپلود کلیک کنید یا فایل را بکشید و رها کنید
                    </span>
                    <span className="text-xs text-muted-foreground">
                      PNG، JPG، GIF تا حجم ۱۰ مگابایت
                    </span>
                  </label>
                </div>
              ) : (
                <div className="relative">
                  <Image
                    src={previewUrl}
                    alt="پیش‌نمایش"
                    width={600}
                    height={400}
                    className="rounded-lg max-h-[400px] w-full object-contain bg-accent/10"
                  />
                  <button
                    type="button"
                    className="absolute top-2 left-2 bg-black/50 text-white p-1 rounded-full"
                    onClick={clearSelectedFile}
                    disabled={isLoading}
                  >
                    <X size={16} />
                  </button>
                </div>
              )}
            </div>

            {/* Items Preview */}
            <div className="space-y-2">
              <Label className="block mb-2">پیش‌نمایش آیتم‌ها</Label>
              <Card className="bg-accent/5">
                <CardContent className="p-4">
                  <h3 className="text-lg font-medium mb-4">
                    آیتم‌های انتخاب شده
                  </h3>
                  <div className="space-y-3">
                    {outfitItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-2 border-b"
                      >
                        <div className="flex items-center">
                          {getBodyPartIcon(item.bodyPart)}
                          <span className="mr-2">{item.name}</span>
                          <span className="text-sm text-muted-foreground">
                            ({item.brand})
                          </span>
                        </div>
                        <div className="text-sm font-medium">
                          {parseInt(item.price).toLocaleString()}{" "}
                          {item.currency}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-6">
          <Button onClick={() => goToStep(1)} variant="outline">
            <ArrowRight className="ml-2 h-4 w-4" />
            مرحله قبل
          </Button>

          <Button
            type="submit"
            className="bg-red-500 hover:bg-red-600 text-white font-medium py-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
            disabled={!selectedFile || isLoading}
          >
            {isLoading ? (
              <>
                <span>در حال آپلود...</span>
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent ml-2"></div>
              </>
            ) : (
              <>
                <span>اشتراک گذاری استایل</span>
                <Upload className="h-5 w-5 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6 md:py-8 max-w-4xl pb-24">
      <h1 className="text-3xl font-bold mb-8 text-center">
        به اشتراک گذاری ست{" "}
      </h1>

      <div className="mb-8">
        <div className="flex items-center justify-center">
          <div className="flex items-center">
            <div className="w-36 flex justify-center">
              <button
                onClick={() => goToStep(1)}
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  currentStep === 1
                    ? "bg-primary text-white"
                    : "bg-primary/20 text-primary cursor-pointer"
                } hover:bg-primary/40 transition-colors`}
              >
                1
              </button>
            </div>
            <div className="w-0 h-1 bg-gray-200 flex-grow mx-1">
              <div
                className={`h-full ${
                  currentStep === 2 ? "bg-primary" : "bg-gray-200"
                }`}
                style={{ width: currentStep === 2 ? "100%" : "0%" }}
              ></div>
            </div>
            <div className="w-36 flex justify-center">
              <button
                onClick={() =>
                  outfitItems.length === 0
                    ? validateStep1WithToast()
                    : goToStep(2)
                }
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  currentStep === 2
                    ? "bg-primary text-white"
                    : canProceedToStep2()
                    ? "bg-primary/30 text-primary cursor-pointer"
                    : "bg-primary/20 text-primary/50"
                } hover:bg-primary/40 transition-colors`}
              >
                2
              </button>
            </div>
          </div>
        </div>
        <div className="flex justify-center mt-2 text-sm">
          <div className="w-36 flex justify-center">
            <button
              onClick={() => goToStep(1)}
              className={`text-center hover:text-primary transition-colors ${
                currentStep === 1
                  ? "text-primary font-bold"
                  : "text-muted-foreground"
              }`}
            >
              آیتم‌ها
            </button>
          </div>
          <div className="w-36 flex justify-center">
            <button
              onClick={() =>
                outfitItems.length === 0
                  ? validateStep1WithToast()
                  : goToStep(2)
              }
              className={`text-center hover:text-primary transition-colors ${
                currentStep === 2
                  ? "text-primary font-bold"
                  : !canProceedToStep2()
                  ? "text-muted-foreground/50"
                  : "text-muted-foreground"
              }`}
            >
              اطلاعات پست
            </button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {currentStep === 1 ? renderStep1() : renderStep2()}
      </form>
    </div>
  );
}
