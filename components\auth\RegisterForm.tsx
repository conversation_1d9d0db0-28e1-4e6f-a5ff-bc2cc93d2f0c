"use client";

import { UseFormReturn } from "react-hook-form";
import { Form, FormField } from "@/components/ui/form";
import { AuthFormField } from "./AuthFormField";
import { AuthButton } from "./AuthButton";
import { User, Mail, Lock, LockKeyhole, UserPlus } from "lucide-react";

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirm_password: string;
}

interface RegisterFormProps {
  form: UseFormReturn<RegisterFormData>;
  onSubmit: (data: RegisterFormData) => void;
  isLoading: boolean;
}

export function RegisterForm({ form, onSubmit, isLoading }: RegisterFormProps) {
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <AuthFormField
              label="نام کاربری"
              icon={<User className="h-5 w-5" />}
              placeholder="نام کاربری خود را وارد کنید"
              field={field}
              disabled={isLoading}
            />
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <AuthFormField
              label="ایمیل"
              icon={<Mail className="h-5 w-5" />}
              placeholder="ایمیل خود را وارد کنید"
              type="email"
              field={field}
              disabled={isLoading}
            />
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <AuthFormField
              label="رمز عبور"
              icon={<Lock className="h-5 w-5" />}
              placeholder="رمز عبور خود را وارد کنید"
              type="password"
              field={field}
              disabled={isLoading}
            />
          )}
        />

        <FormField
          control={form.control}
          name="confirm_password"
          render={({ field }) => (
            <AuthFormField
              label="تکرار رمز عبور"
              icon={<LockKeyhole className="h-5 w-5" />}
              placeholder="رمز عبور خود را تکرار کنید"
              type="password"
              field={field}
              disabled={isLoading}
            />
          )}
        />

        <AuthButton
          type="submit"
          variant="secondary"
          isLoading={isLoading}
          loadingText="در حال ثبت‌نام..."
        >
          <div className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            <span>ثبت‌نام</span>
          </div>
        </AuthButton>
      </form>
    </Form>
  );
}
