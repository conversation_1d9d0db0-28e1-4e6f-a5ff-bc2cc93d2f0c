"use client";

import { UseFormReturn } from "react-hook-form";
import { Form, FormField } from "@/components/ui/form";
import { AuthFormField } from "./AuthFormField";
import { AuthButton } from "./AuthButton";
import { <PERSON>h, CheckCircle, RotateCcw, ArrowLeft } from "lucide-react";

interface VerificationFormData {
  code: string;
}

interface VerificationFormProps {
  form: UseFormReturn<VerificationFormData>;
  onSubmit: (data: VerificationFormData) => void;
  onResendCode: () => void;
  onBackToLogin: () => void;
  isLoading: boolean;
}

export function VerificationForm({
  form,
  onSubmit,
  onResendCode,
  onBackToLogin,
  isLoading
}: VerificationFormProps) {
  return (
    <div className="bg-transparent p-6 rounded-lg border-3 border-border shadow-[6px_6px_0_0] shadow-border mb-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <AuthFormField
                label="کد تایید"
                icon={<Hash className="h-5 w-5" />}
                placeholder="کد تایید را وارد کنید"
                field={field}
                disabled={isLoading}
              />
            )}
          />

          <AuthButton
            type="submit"
            variant="primary"
            isLoading={isLoading}
            loadingText="در حال تایید..."
          >
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              <span>تایید حساب کاربری</span>
            </div>
          </AuthButton>
        </form>
      </Form>

      <div className="mt-6 text-center space-y-3">
        <AuthButton
          variant="outline"
          onClick={onResendCode}
          disabled={isLoading}
          className="text-sm"
        >
          <div className="flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            <span>ارسال مجدد کد تایید</span>
          </div>
        </AuthButton>
      </div>

      <div className="mt-4 text-center">
        <AuthButton
          variant="ghost"
          onClick={onBackToLogin}
          disabled={isLoading}
          className="text-sm"
        >
          <div className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            <span>بازگشت به صفحه ورود</span>
          </div>
        </AuthButton>
      </div>
    </div>
  );
}
