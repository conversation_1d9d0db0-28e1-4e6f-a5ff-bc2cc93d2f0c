"use client";

import Link from "next/link";
import Image from "next/image";
import logo from "@/public/temp_logo.png";
import { usePathname } from "next/navigation";
import {
  AiOutlineHome,
  AiOutlineSearch,
  AiOutlinePlus,
  AiOutlineBell,
  AiOutlineUser,
  AiOutlineLogin,
  AiOutlineLogout,
  AiOutlineEdit,
} from "react-icons/ai";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { WEBSITE_NAME } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";

export function MobileNavbar() {
  const pathname = usePathname();
  const [logoutLoading, setLogoutLoading] = useState(false);
  const { isAuthenticated, logout, loading } = useAuthContext();

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      // Execute logout
      await logout();
      // Wait slightly for smooth UI transition
      setTimeout(() => {
        setLogoutLoading(false);
      }, 300);
    } catch {
      setLogoutLoading(false);
    }
  };

  return (
    <div className="flex h-20 items-center justify-around border-t border-platinum bg-white py-3 md:hidden">
      <NavItem
        href="/"
        isActive={pathname === "/"}
        icon={<AiOutlineHome className="h-6 w-6" />}
        label="خانه"
        activeColor="bg-moonstone/10 border-moonstone text-moonstone"
        hoverColor="hover:text-moonstone"
      />
      <NavItem
        href="/search"
        isActive={pathname === "/search"}
        icon={<AiOutlineSearch className="h-6 w-6" />}
        label="جستجو"
        activeColor="bg-powder-blue/10 border-powder-blue text-powder-blue"
        hoverColor="hover:text-powder-blue"
      />
      <NavItem
        href="/create"
        isActive={pathname === "/create"}
        icon={<AiOutlinePlus className="h-6 w-6" />}
        label="ایجاد"
        activeColor="bg-melon/10 border-melon text-melon"
        hoverColor="hover:text-melon"
      />

      {!loading &&
        (isAuthenticated ? (
          <button
            onClick={handleLogout}
            disabled={logoutLoading}
            className="flex flex-col items-center justify-center px-3"
          >
            <div className="flex items-center justify-center rounded-lg p-2 text-muted-foreground hover:text-melon hover:scale-105 transition-all border border-transparent hover:border-melon hover:bg-melon/10">
              {logoutLoading ? (
                <AiOutlineLoading3Quarters className="h-6 w-6 animate-spin" />
              ) : (
                <AiOutlineLogout className="h-6 w-6" />
              )}
            </div>
            <span className="text-xs font-bold mt-1">خروج</span>
          </button>
        ) : (
          <NavItem
            href="/auth"
            isActive={pathname === "/auth"}
            icon={<AiOutlineLogin className="h-6 w-6" />}
            label="ورود"
            activeColor="bg-charcoal/10 border-charcoal text-charcoal"
            hoverColor="hover:text-charcoal"
          />
        ))}
    </div>
  );
}

export function DesktopNavbar() {
  const pathname = usePathname();
  const [logoutLoading, setLogoutLoading] = useState(false);
  const { isAuthenticated, userData, logout, loading } = useAuthContext();

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      // Execute logout (which will clear tokens, cookies, and redirect)
      await logout();
      // We add a delay for smooth UI transition
      setTimeout(() => {
        setLogoutLoading(false);
      }, 300);
    } catch {
      setLogoutLoading(false);
    }
  };

  // Format user avatar URL
  const avatarUrl = userData?.profile_photo
    ? `${process.env.NEXT_PUBLIC_API_BASE || ""}${
        userData.profile_photo
      }?v=${Date.now()}`
    : userData?.avatarUrl || "/images/1.jpg";

  // Get user initials for avatar fallback
  const userInitials = userData?.username
    ? userData.username.slice(0, 2).toUpperCase()
    : userData?.first_name && userData.last_name
    ? `${userData.first_name[0]}${userData.last_name[0]}`.toUpperCase()
    : "کا";

  return (
    <div className="hidden md:flex md:h-18 md:items-center md:justify-between md:border-b md:border-platinum md:bg-white md:px-5 md:py-3 lg:px-8">
      <div className="flex items-center">
        <Link href="/" className="ml-5 flex items-center space-x-2">
          <div className="border border-platinum shadow-sm rounded-lg overflow-hidden bg-white">
            <Image src={logo} alt="logo" className="h-14 w-16" />
          </div>
          <span className="text-xl font-bold ml-2 text-charcoal">
            {WEBSITE_NAME}
          </span>
        </Link>
        <nav className="flex items-center space-x-2 lg:space-x-3">
          <Link
            href="/"
            className={cn(
              "xl:text-base text-sm font-medium px-4 py-2 transition-all hover:text-moonstone rounded-lg",
              pathname === "/"
                ? "text-moonstone bg-moonstone/10 border border-moonstone"
                : "text-muted-foreground"
            )}
          >
            خانه
          </Link>
          <Link
            href="/search"
            className={cn(
              "xl:text-base text-sm font-medium px-4 py-2 transition-all hover:text-powder-blue rounded-lg",
              pathname === "/search"
                ? "text-powder-blue bg-powder-blue/10 border border-powder-blue"
                : "text-muted-foreground"
            )}
          >
            جستجو
          </Link>
          <Link
            href="/create"
            className={cn(
              "xl:text-base text-sm font-medium px-4 py-2 transition-all hover:text-melon rounded-lg",
              pathname === "/create"
                ? "text-melon bg-melon/10 border border-melon"
                : "text-muted-foreground"
            )}
          >
            ایجاد
          </Link>
        </nav>
      </div>
      <div className="flex items-center space-x-4">
        {!loading &&
          (isAuthenticated ? (
            <>
              <Link
                href="/notifications"
                className={cn(
                  "flex items-center rounded-md p-2 transition-all border-2 hover:border-accent ",
                  pathname === "/notifications"
                    ? "bg-accent text-accent-foreground border-accent shadow-[3px_3px_0_0] shadow-accent"
                    : "text-muted-foreground border-transparent"
                )}
              >
                <AiOutlineBell className="h-5 w-5" />
              </Link>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="p-0 h-auto rounded-md flex border-0 shadow-none hover:bg-transparent"
                  >
                    <Avatar className="h-10 w-10 border border-platinum hover:shadow-md transition-all">
                      <AvatarImage src={avatarUrl} alt="پروفایل" />
                      <AvatarFallback>{userInitials}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="start"
                  className="w-52 border border-platinum shadow-lg rounded-lg"
                >
                  <Link href="/profile">
                    <DropdownMenuItem
                      className="cursor-pointer text-right hover:bg-powder-blue/10 h-11 text-base font-medium"
                      dir="rtl"
                    >
                      {" "}
                      <AiOutlineUser className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" />
                      <span>پروفایل</span>
                    </DropdownMenuItem>
                  </Link>
                  <Link href="/profile/edit">
                    <DropdownMenuItem
                      dir="rtl"
                      className="cursor-pointer hover:bg-powder-blue/10 h-11 text-base font-medium"
                    >
                      <AiOutlineEdit className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" />
                      <span>ویرایش پروفایل</span>
                    </DropdownMenuItem>
                  </Link>
                  <DropdownMenuSeparator className="h-px bg-platinum" />
                  <DropdownMenuItem
                    dir="rtl"
                    className="cursor-pointer flex justify-between text-melon hover:bg-melon/10 h-11 text-base font-medium"
                    onClick={handleLogout}
                    disabled={logoutLoading}
                  >
                    <span>خروج</span>
                    {logoutLoading ? (
                      <AiOutlineLoading3Quarters className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0 animate-spin" />
                    ) : (
                      <AiOutlineLogout className="mr-2 h-5 w-5 rtl:ml-2 rtl:mr-0" />
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <Link href="/auth">
              <Button
                variant="default"
                size="default"
                className="gap-2 font-bold text-base border-3 shadow-[4px_4px_0_0] shadow-primary/50 transition-all duration-200  hover:shadow-[6px_6px_0_0] hover:shadow-primary/60"
              >
                <span>ورود</span>
                <LogIn className="h-5 w-5 rotate-180" />
              </Button>
            </Link>
          ))}
      </div>
    </div>
  );
}

type NavItemProps = {
  href: string;
  isActive: boolean;
  icon: React.ReactNode;
  label: string;
  activeColor?: string;
  hoverColor?: string;
};

function NavItem({
  href,
  isActive,
  icon,
  label,
  activeColor = "bg-primary/20 border-primary text-primary shadow-primary",
  hoverColor = "hover:text-primary",
}: NavItemProps) {
  return (
    <Link
      href={href}
      className="flex flex-col items-center justify-center px-3"
    >
      <div
        className={cn(
          "flex items-center justify-center rounded-lg p-2 transition-all hover:scale-105 border",
          isActive
            ? `${activeColor}`
            : `text-muted-foreground ${hoverColor} border-transparent hover:border-current hover:bg-current/10`
        )}
      >
        {icon}
      </div>
      <span
        className={cn(
          "text-xs mt-1 font-bold",
          isActive
            ? activeColor.split(" ").find((c) => c.startsWith("text-")) ||
                "text-primary"
            : ""
        )}
      >
        {label}
      </span>
    </Link>
  );
}
