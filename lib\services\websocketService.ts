import { EventEmitter } from "events";
import authService from "./authService";

// Simple profile data interface
export interface ProfileData {
  id?: string | number;
  username?: string;
  email?: string;
  bio?: string;
  profile_photo?: string;
  first_name?: string | null;
  last_name?: string | null;
  followers_count?: number;
  following_count?: number;
  date_joined?: number;
  is_active?: boolean;
  is_staff?: boolean;
  gender?: string | null;
  phone_number?: string | null;
  [key: string]: unknown;
}

// Update profile payload interface
export interface UpdateProfilePayload {
  job: string;
  first_name?: string | null;
  last_name?: string | null;
  bio?: string | null;
  photo?: string | null;
  [key: string]: unknown;
}

// WebSocket message interface
export interface WebSocketMessage {
  job: string;
  [key: string]: unknown;
}

// WebSocket events
export enum WebSocketEvent {
  PROFILE_UPDATED = "profile_updated",
  ERROR = "error",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  MESSAGE = "message",
}

// WebSocket service singleton
class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private eventEmitter = new EventEmitter();
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1500;
  private messageQueue: WebSocketMessage[] = [];
  private isConnecting = false;
  private _lastProfileRequest: number = 0;

  private constructor() {}

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  // Connect to WebSocket server
  public connect(): void {
    const accessToken = authService.getToken();
    if (!accessToken) {
      console.error("No access token available for WebSocket connection");
      this.isConnecting = false;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      this.reconnectAttempts = 0;
      return;
    }

    if (
      this.ws &&
      (this.ws.readyState === WebSocket.OPEN ||
        this.ws.readyState === WebSocket.CONNECTING)
    ) {
      return;
    }

    if (this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL;
    if (!wsUrl) {
      console.error("WebSocket URL not configured in environment variables");
      this.isConnecting = false;
      this.eventEmitter.emit(
        WebSocketEvent.ERROR,
        new Error("WebSocket configuration missing")
      );
      return;
    }

    try {
      const wsFullUrl = `ws://${wsUrl}/?access_token=${accessToken}`;
      this.ws = new WebSocket(wsFullUrl);
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
    } catch (error) {
      console.error("Error creating WebSocket connection:", error);
      this.isConnecting = false;
      this.eventEmitter.emit(WebSocketEvent.ERROR, error);
    }
  }

  // Handle connection open
  private handleOpen(): void {
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.eventEmitter.emit(WebSocketEvent.CONNECTED);

    // Process queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }

  // Handle incoming messages
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      // Only log non-heartbeat messages
      if (!data.job || data.job !== "heartbeat") {
        console.log("WebSocket received message:", data);
      }
      this.eventEmitter.emit(WebSocketEvent.MESSAGE, data);

      if (data && data.job) {
        // Handle specific job responses
        if (data.job === "get_profile" && data.success && data.user) {
          if (typeof window !== "undefined") {
            localStorage.setItem("user_data", JSON.stringify(data.user));
          }
          // Emit specific event for get_profile job
          this.eventEmitter.emit(data.job, data);
          // Also emit profile_updated for consistency
          this.eventEmitter.emit(WebSocketEvent.PROFILE_UPDATED, data.user);
        } else if (data.job === "profile" && data.success && data.user) {
          if (typeof window !== "undefined") {
            localStorage.setItem("user_data", JSON.stringify(data.user));
          }
          this.eventEmitter.emit(WebSocketEvent.PROFILE_UPDATED, data.user);
        }
        // For any other job, emit with job name as event
        else if (data.job) {
          this.eventEmitter.emit(data.job, data);
        }
      }
    } catch (error) {
      console.error("Error parsing WebSocket message:", error);
    }
  }

  // Handle WebSocket errors
  private handleError(error: Event): void {
    console.error("WebSocket error:", error);
    this.isConnecting = false;
    this.eventEmitter.emit(WebSocketEvent.ERROR, error);
  }

  // Handle connection close
  private handleClose(event: CloseEvent): void {
    // Only log non-normal closures
    if (event.code !== 1000) {
      console.log("WebSocket closed:", event.code, event.reason);
    }
    this.isConnecting = false;
    this.ws = null;
    this.eventEmitter.emit(WebSocketEvent.DISCONNECTED, event);

    if (
      event.code !== 1000 &&
      this.reconnectAttempts < this.maxReconnectAttempts
    ) {
      this.scheduleReconnect();
    }
  }

  // Schedule reconnection
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(1.3, this.reconnectAttempts),
      30000
    );

    this.reconnectTimer = setTimeout(() => {
      if (!authService.getToken()) {
        this.reconnectTimer = null;
        this.reconnectAttempts = 0;
        return;
      }

      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  // Send message to server
  public sendMessage(message: WebSocketMessage): boolean {
    if (!this.isConnected()) {
      this.messageQueue.push(message);
      if (!this.isConnecting) {
        this.connect();
      }
      return false;
    }

    try {
      this.ws?.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error("Error sending WebSocket message:", error);
      this.eventEmitter.emit(WebSocketEvent.ERROR, error);
      return false;
    }
  }

  // Check connection status
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  // Close connection
  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close(1000, "Client disconnected");
      this.ws = null;
    }
  }

  // Event listener methods
  public on(
    event: WebSocketEvent | string,
    listener: (data: unknown) => void
  ): void {
    this.eventEmitter.on(event, listener);
  }

  public off(
    event: WebSocketEvent | string,
    listener: (data: unknown) => void
  ): void {
    this.eventEmitter.off(event, listener);
  }

  public once(
    event: WebSocketEvent | string,
    listener: (data: unknown) => void
  ): void {
    this.eventEmitter.once(event, listener);
  }

  // Request profile data
  public requestProfileData(): boolean {
    // Check if we already sent a profile request recently
    const lastRequestTime = this._lastProfileRequest || 0;
    const now = Date.now();
    const MIN_REQUEST_INTERVAL = 2000; // 2 seconds minimum between requests

    if (now - lastRequestTime < MIN_REQUEST_INTERVAL) {
      console.log("Profile request throttled, too many requests");
      return false;
    }

    this._lastProfileRequest = now;
    return this.sendMessage({ job: "get_profile" });
  }

  // Subscribe to a job (for backward compatibility)
  public subscribe(job: string): void {
    if (!authService.getToken()) {
      console.error(
        "Cannot subscribe to job - No authentication token available"
      );
      return;
    }

    if (this.isConnected()) {
      this.sendMessage({ job });
    } else {
      this.connect();
    }
  }

  // Update profile data
  public updateProfile(
    profileData: Omit<UpdateProfilePayload, "job">
  ): boolean {
    return this.sendMessage({
      job: "update_profile",
      ...profileData,
    });
  }

  // Reset service state
  public reset(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.isConnecting = false;
    this.messageQueue = [];
  }
}

// Export the singleton instance
export default WebSocketService.getInstance();
