"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Heart, MessageCircle, Share2 } from "lucide-react";
import { cn } from "@/lib/utils";

type MasonryGridProps = {
  images: string[];
  className?: string;
};

export function MasonryGrid({ images, className }: MasonryGridProps) {
  const [columns, setColumns] = useState(4);

  useEffect(() => {
    const updateColumns = () => {
      if (window.innerWidth < 640) {
        setColumns(2);
      } else if (window.innerWidth < 768) {
        setColumns(3);
      } else if (window.innerWidth < 1024) {
        setColumns(4);
      } else if (window.innerWidth < 1280) {
        setColumns(5);
      } else {
        setColumns(6);
      }
    };

    updateColumns();
    window.addEventListener("resize", updateColumns);
    return () => window.removeEventListener("resize", updateColumns);
  }, []);

  // Create column arrays
  const columnArrays: string[][] = Array.from({ length: columns }, () => []);

  // Distribute images into columns
  images.forEach((image, index) => {
    const columnIndex = index % columns;
    columnArrays[columnIndex].push(image);
  });

  return (
    <div className={cn("flex w-full gap-3 md:gap-5", className)}>
      {columnArrays.map((column, columnIndex) => (
        <div
          key={`column-${columnIndex}`}
          className="flex flex-1 flex-col gap-3 md:gap-5"
        >
          {column.map((image, imageIndex) => (
            <FashionImageCard
              key={`${columnIndex}-${imageIndex}`}
              imageSrc={image}
              id={`${columnIndex}-${imageIndex}`}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

type FashionImageCardProps = {
  imageSrc: string;
  id: string;
};

function FashionImageCard({ imageSrc, id }: FashionImageCardProps) {
  // Use a client-side only hover state
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="relative overflow-hidden rounded-lg border border-platinum shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/pin/${id}`}>
        <div className="relative overflow-hidden">
          <Image
            src={imageSrc}
            alt="استایل مد"
            width={500}
            height={500}
            className="w-full object-cover"
            style={{ aspectRatio: "auto", height: "auto" }}
          />
          {isHovered && (
            <div className="absolute inset-0 bg-black/30 dark:bg-black/50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 hover-content">
              <div
                className="absolute bottom-4 left-0 right-0 px-4 w-full"
                dir="ltr"
              >
                <div className="flex justify-between items-center">
                  <button className="p-2 rounded-lg border border-white/20 bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all shadow-sm">
                    <Heart className="w-5 h-5 text-charcoal" />
                  </button>

                  <div className="flex gap-2">
                    <button className="p-2 rounded-lg border border-white/20 bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all shadow-sm">
                      <MessageCircle className="w-5 h-5 text-charcoal" />
                    </button>
                    <button className="p-2 rounded-lg border border-white/20 bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all shadow-sm">
                      <Share2 className="w-5 h-5 text-charcoal" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Link>
    </div>
  );
}
