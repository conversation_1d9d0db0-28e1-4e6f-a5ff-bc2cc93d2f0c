import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import authService, { UserData } from "../services/authService";
import webSocketService, { WebSocketEvent } from "../services/websocketService";

export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [hasTempTokens, setHasTempTokens] = useState<boolean>(false);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Function to set authentication tokens and user data
  const setAuthTokens = (
    accessToken: string,
    refreshToken: string,
    userData?: UserData
  ) => {
    try {
      // Store tokens using the proper function in authService
      // (login is NOT the right function to use here)
      if (typeof window !== "undefined") {
        localStorage.setItem("access_token", accessToken);
        localStorage.setItem("refresh_token", refreshToken);

        if (userData) {
          localStorage.setItem("user_data", JSON.stringify(userData));
        }
      }

      // Update user data if provided
      if (userData) {
        setUserData(userData);
      }

      // Update authentication state
      setIsAuthenticated(true);

      // Immediately check auth state to ensure everything is updated
      setTimeout(checkAuth, 100);
    } catch (e) {
      console.error("Error setting auth tokens:", e);
      toast.error("خطا در ذخیره اطلاعات ورود", {
        position: "top-center",
      });
    }
  };

  // Function to check authentication status
  const checkAuth = () => {
    try {
      // Check if user is authenticated using authService
      const isAuth = authService.isAuthenticated();
      setIsAuthenticated(isAuth);

      // Check if user has temporary tokens
      const hasTempAuth = authService.hasTempTokens();
      setHasTempTokens(hasTempAuth);

      if (isAuth) {
        // Get user data from authService
        const storedUserData = authService.getUserData();
        if (storedUserData) {
          setUserData(storedUserData);
        } else {
          setUserData(null);
        }
      } else {
        // Not authenticated
        setIsAuthenticated(false);
        setUserData(null);
      }
    } catch (e) {
      console.error("Error checking auth status:", e);
      setIsAuthenticated(false);
      setUserData(null);
    } finally {
      // Indicate that we've completed the auth check
      setLoading(false);
    }
  };

  // Function to logout the user
  const logout = () => {
    try {
      // Use authService to handle logout
      authService.logout();

      // Update state
      setIsAuthenticated(false);
      setUserData(null);
      setHasTempTokens(false);

      // Set a logout flag in sessionStorage to prevent the error toast
      sessionStorage.setItem("from_logout", "true");

      // Redirect to home page after a short delay
      setTimeout(() => {
        router.push("/");
      }, 100);
    } catch (e) {
      console.error("Error during logout:", e);
      toast.error("خطا در خروج از حساب کاربری", {
        position: "top-center",
      });
    }
  };

  // Check authentication on initial load
  useEffect(() => {
    checkAuth();
  }, []);

  // Handle WebSocket profile updates
  useEffect(() => {
    if (isAuthenticated) {
      const handleProfileUpdate = (data: unknown) => {
        const profileData = data as UserData;
        setUserData(profileData);
        if (typeof window !== "undefined") {
          localStorage.setItem("user_data", JSON.stringify(profileData));
        }
      };

      webSocketService.on(WebSocketEvent.PROFILE_UPDATED, handleProfileUpdate);
      webSocketService.requestProfileData();

      return () => {
        webSocketService.off(
          WebSocketEvent.PROFILE_UPDATED,
          handleProfileUpdate
        );
      };
    }
  }, [isAuthenticated]);

  return {
    isAuthenticated,
    hasTempTokens,
    userData,
    loading,
    logout,
    checkAuth,
    setAuthTokens,
  };
}

export default useAuth;
