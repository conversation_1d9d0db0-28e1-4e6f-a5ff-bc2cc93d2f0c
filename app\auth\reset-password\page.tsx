"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { motion } from "framer-motion";
import authService from "@/lib/services/authService";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Eye, EyeOff, ArrowLeft } from "lucide-react";

// Validation schemas
const emailSchema = z.object({
  email: z.string().email({ message: "آدرس ایمیل معتبر نیست" }),
});

const verifyCodeSchema = z.object({
  code: z
    .string()
    .min(1, { message: "کد تأیید الزامی است" })
    .refine((val) => !isNaN(Number(val)), {
      message: "کد تأیید باید عدد باشد",
    }),
});

const newPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, { message: "رمز عبور باید حداقل ۸ کاراکتر باشد" }),
    confirmPassword: z
      .string()
      .min(8, { message: "تأیید رمز عبور باید حداقل ۸ کاراکتر باشد" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "رمز عبور و تأیید آن باید یکسان باشند",
    path: ["confirmPassword"],
  });

type EmailFormValues = z.infer<typeof emailSchema>;
type VerifyCodeFormValues = z.infer<typeof verifyCodeSchema>;
type NewPasswordFormValues = z.infer<typeof newPasswordSchema>;

// Reset password steps
enum ResetStep {
  REQUEST = "request",
  VERIFY = "verify",
  RESET = "reset",
  SUCCESS = "success",
}

export default function ResetPasswordPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<ResetStep>(ResetStep.REQUEST);
  const [isLoading, setIsLoading] = useState(false);
  const [processId, setProcessId] = useState<string>("");
  const [userEmail, setUserEmail] = useState<string>("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Email form
  const emailForm = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  // Verification code form
  const verifyCodeForm = useForm<VerifyCodeFormValues>({
    resolver: zodResolver(verifyCodeSchema),
    defaultValues: {
      code: "",
    },
  });

  // New password form
  const newPasswordForm = useForm<NewPasswordFormValues>({
    resolver: zodResolver(newPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Handle sending reset code
  const onRequestSubmit = async (data: EmailFormValues) => {
    setIsLoading(true);
    try {
      const response = await authService.requestPasswordReset(data.email);

      if (response.success) {
        setUserEmail(data.email);
        setProcessId(response.processId || "");
        setCurrentStep(ResetStep.VERIFY);
        toast.success("کد بازیابی ارسال شد", {
          description: "کد بازیابی رمز عبور به ایمیل شما ارسال شد.",
          position: "top-center",
        });
      } else {
        toast.error("خطا", {
          description: "خطا در ارسال کد بازیابی رمز عبور",
          position: "top-center",
        });
      }
    } catch (error) {
      console.error("Error requesting reset code:", error);
      toast.error("خطا در ارسال درخواست", {
        description: "مشکلی در ارسال درخواست بازیابی رمز عبور پیش آمد.",
        position: "top-center",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle verifying reset code
  const onVerifySubmit = async (data: VerifyCodeFormValues) => {
    setIsLoading(true);
    try {
      const response = await authService.verifyResetCode(
        processId,
        parseInt(data.code)
      );

      if (response.success) {
        setCurrentStep(ResetStep.RESET);
        toast.success("کد تأیید شد", {
          description: "اکنون می‌توانید رمز عبور جدید خود را تعیین کنید.",
          position: "top-center",
        });
      } else {
        toast.error("خطا", {
          description: "کد تأیید نامعتبر است",
          position: "top-center",
        });
      }
    } catch (error) {
      console.error("Error verifying code:", error);
      toast.error("خطا در تأیید کد", {
        description: "مشکلی در تأیید کد بازیابی رمز عبور پیش آمد.",
        position: "top-center",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle setting new password
  const onResetSubmit = async (data: NewPasswordFormValues) => {
    setIsLoading(true);
    try {
      const response = await authService.resetPassword(
        processId,
        data.password,
        data.confirmPassword
      );

      if (response.success) {
        setCurrentStep(ResetStep.SUCCESS);
        toast.success("رمز عبور با موفقیت تغییر کرد", {
          description: "می‌توانید با رمز عبور جدید وارد شوید.",
          position: "top-center",
        });
      } else {
        toast.error("خطا", {
          description: "خطا در تغییر رمز عبور",
          position: "top-center",
        });
      }
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error("خطا در تغییر رمز عبور", {
        description: "مشکلی در تغییر رمز عبور پیش آمد.",
        position: "top-center",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Request reset code again
  const handleResendCode = async () => {
    setIsLoading(true);
    try {
      const response = await authService.requestPasswordReset(userEmail);

      if (response.success) {
        setProcessId(response.processId || "");
        toast.success("کد بازیابی مجدداً ارسال شد", {
          description: "کد جدید بازیابی رمز عبور به ایمیل شما ارسال شد.",
          position: "top-center",
        });
      } else {
        toast.error("خطا", {
          description: "خطا در ارسال مجدد کد بازیابی",
          position: "top-center",
        });
      }
    } catch (error) {
      console.error("Error resending code:", error);
      toast.error("خطا در ارسال مجدد کد", {
        description: "مشکلی در ارسال مجدد کد بازیابی پیش آمد.",
        position: "top-center",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-[calc(100vh-4rem)] md:min-h-[calc(100vh-4.5rem)] flex flex-col md:py-0 md:items-center md:justify-center transition-all">
      <motion.div
        className="w-full md:max-w-md px-4 md:px-6 mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="border-gray-300 dark:border-gray-600 shadow-lg overflow-hidden">
          <CardHeader className="pb-2">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="flex items-center mb-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 ml-2"
                  onClick={() => {
                    if (currentStep === ResetStep.REQUEST) {
                      router.push("/auth");
                    } else if (currentStep === ResetStep.VERIFY) {
                      setCurrentStep(ResetStep.REQUEST);
                    } else if (currentStep === ResetStep.RESET) {
                      setCurrentStep(ResetStep.VERIFY);
                    }
                  }}
                  disabled={currentStep === ResetStep.SUCCESS || isLoading}
                >
                  <ArrowLeft className="h-4 w-4 rotate-180" />
                </Button>
                <CardTitle className="text-2xl font-semibold">
                  {currentStep === ResetStep.REQUEST && "بازیابی رمز عبور"}
                  {currentStep === ResetStep.VERIFY && "تأیید کد بازیابی"}
                  {currentStep === ResetStep.RESET && "تعیین رمز عبور جدید"}
                  {currentStep === ResetStep.SUCCESS && "بازیابی موفق"}
                </CardTitle>
              </div>
              <CardDescription>
                {currentStep === ResetStep.REQUEST &&
                  "ایمیل خود را وارد کنید تا کد بازیابی برای شما ارسال شود."}
                {currentStep === ResetStep.VERIFY &&
                  "کد بازیابی ارسال شده به ایمیل خود را وارد کنید."}
                {currentStep === ResetStep.RESET &&
                  "رمز عبور جدید خود را تعیین کنید."}
                {currentStep === ResetStep.SUCCESS &&
                  "رمز عبور شما با موفقیت تغییر کرد."}
              </CardDescription>
            </motion.div>
          </CardHeader>
          <CardContent className="pt-4">
            {currentStep === ResetStep.REQUEST && (
              <motion.div
                key="request"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Form {...emailForm}>
                  <form
                    onSubmit={emailForm.handleSubmit(onRequestSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={emailForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-bold mb-1">
                            ایمیل
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="ایمیل خود را وارد کنید"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      className="w-full hover:bg-primary/85 font-bold"
                      disabled={isLoading}
                      size="lg"
                    >
                      {isLoading ? "در حال ارسال..." : "ارسال کد بازیابی"}
                    </Button>
                  </form>
                </Form>
              </motion.div>
            )}

            {currentStep === ResetStep.VERIFY && (
              <motion.div
                key="verify"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Form {...verifyCodeForm}>
                  <form
                    onSubmit={verifyCodeForm.handleSubmit(onVerifySubmit)}
                    className="space-y-4"
                  >
                    <div className="text-sm text-center mb-4">
                      کد بازیابی به ایمیل{" "}
                      <span className="font-bold">{userEmail}</span> ارسال شد.
                    </div>
                    <FormField
                      control={verifyCodeForm.control}
                      name="code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-bold mb-1">
                            کد بازیابی
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="کد بازیابی را وارد کنید"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      className="w-full hover:bg-primary/85 font-bold"
                      disabled={isLoading}
                      size="lg"
                    >
                      {isLoading ? "در حال تأیید..." : "تأیید کد"}
                    </Button>

                    <div className="text-center mt-4">
                      <Button
                        type="button"
                        variant="link"
                        onClick={handleResendCode}
                        disabled={isLoading}
                        className="text-sm"
                      >
                        ارسال مجدد کد بازیابی
                      </Button>
                    </div>
                  </form>
                </Form>
              </motion.div>
            )}

            {currentStep === ResetStep.RESET && (
              <motion.div
                key="reset"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Form {...newPasswordForm}>
                  <form
                    onSubmit={newPasswordForm.handleSubmit(onResetSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={newPasswordForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-bold mb-1">
                            رمز عبور جدید
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type={showPassword ? "text" : "password"}
                                placeholder="رمز عبور جدید را وارد کنید"
                                {...field}
                                disabled={isLoading}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute inset-y-0 left-0 px-3"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={isLoading}
                              >
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={newPasswordForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-bold mb-1">
                            تأیید رمز عبور جدید
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input
                                type={showConfirmPassword ? "text" : "password"}
                                placeholder="رمز عبور جدید را مجدداً وارد کنید"
                                {...field}
                                disabled={isLoading}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="absolute inset-y-0 left-0 px-3"
                                onClick={() =>
                                  setShowConfirmPassword(!showConfirmPassword)
                                }
                                disabled={isLoading}
                              >
                                {showConfirmPassword ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button
                      type="submit"
                      className="w-full hover:bg-primary/85 font-bold"
                      disabled={isLoading}
                      size="lg"
                    >
                      {isLoading ? "در حال ذخیره..." : "تغییر رمز عبور"}
                    </Button>
                  </form>
                </Form>
              </motion.div>
            )}

            {currentStep === ResetStep.SUCCESS && (
              <motion.div
                key="success"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="text-center py-4"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-12 w-12 text-green-600 dark:text-green-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">
                  رمز عبور با موفقیت تغییر کرد
                </h3>
                <p className="text-muted-foreground mb-4">
                  اکنون می‌توانید با رمز عبور جدید وارد حساب کاربری خود شوید.
                </p>
                <Button
                  onClick={() => router.push("/auth")}
                  className="px-8"
                  size="lg"
                >
                  ورود به حساب کاربری
                </Button>
              </motion.div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            {/* <Link
              href="/auth"
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              بازگشت به صفحه ورود
            </Link> */}
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
