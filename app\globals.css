@import "tailwindcss";
@import "tw-animate-css";

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Minimalistic Theme - Light Mode with Custom Color Palette */
  --radius: 0.75rem; /* rounded-lg equivalent */

  /* Custom Color Palette */
  --platinum: #d8dbe2;
  --platinum-rgb: 216, 219, 226;
  --powder-blue: #a9bcd0;
  --powder-blue-rgb: 169, 188, 208;
  --moonstone: #58a4b0;
  --moonstone-rgb: 88, 164, 176;
  --charcoal: #373f51;
  --charcoal-rgb: 55, 63, 81;
  --melon: #daa49a;
  --melon-rgb: 218, 164, 154;

  /* Theme Variables */
  --background: #ffffff;
  --background-rgb: 255, 255, 255;
  --foreground: var(--charcoal);
  --foreground-rgb: var(--charcoal-rgb);
  --card: #ffffff;
  --card-rgb: 255, 255, 255;
  --card-foreground: var(--charcoal);
  --card-foreground-rgb: var(--charcoal-rgb);
  --popover: #ffffff;
  --popover-rgb: 255, 255, 255;
  --popover-foreground: var(--charcoal);
  --popover-foreground-rgb: var(--charcoal-rgb);
  --primary: var(--moonstone);
  --primary-rgb: var(--moonstone-rgb);
  --primary-foreground: #ffffff;
  --primary-foreground-rgb: 255, 255, 255;
  --secondary: var(--platinum);
  --secondary-rgb: var(--platinum-rgb);
  --secondary-foreground: var(--charcoal);
  --secondary-foreground-rgb: var(--charcoal-rgb);
  --muted: var(--platinum);
  --muted-rgb: var(--platinum-rgb);
  --muted-foreground: var(--charcoal);
  --muted-foreground-rgb: var(--charcoal-rgb);
  --accent: var(--powder-blue);
  --accent-rgb: var(--powder-blue-rgb);
  --accent-foreground: var(--charcoal);
  --accent-foreground-rgb: var(--charcoal-rgb);
  --destructive: var(--melon);
  --destructive-rgb: var(--melon-rgb);
  --destructive-foreground: #ffffff;
  --destructive-foreground-rgb: 255, 255, 255;
  --border: var(--platinum);
  --border-rgb: var(--platinum-rgb);
  --input: #ffffff;
  --input-rgb: 255, 255, 255;
  --ring: var(--moonstone);
  --ring-rgb: var(--moonstone-rgb);
  --chart-1: var(--moonstone);
  --chart-1-rgb: var(--moonstone-rgb);
  --chart-2: var(--powder-blue);
  --chart-2-rgb: var(--powder-blue-rgb);
  --chart-3: var(--melon);
  --chart-3-rgb: var(--melon-rgb);
  --chart-4: var(--platinum);
  --chart-4-rgb: var(--platinum-rgb);
  --chart-5: var(--charcoal);
  --chart-5-rgb: var(--charcoal-rgb);
  --sidebar: #ffffff;
  --sidebar-rgb: 255, 255, 255;
  --sidebar-foreground: var(--charcoal);
  --sidebar-foreground-rgb: var(--charcoal-rgb);
  --sidebar-primary: var(--moonstone);
  --sidebar-primary-rgb: var(--moonstone-rgb);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-primary-foreground-rgb: 255, 255, 255;
  --sidebar-accent: var(--powder-blue);
  --sidebar-accent-rgb: var(--powder-blue-rgb);
  --sidebar-accent-foreground: var(--charcoal);
  --sidebar-accent-foreground-rgb: var(--charcoal-rgb);
  --sidebar-border: var(--platinum);
  --sidebar-border-rgb: var(--platinum-rgb);
  --sidebar-ring: var(--moonstone);
  --sidebar-ring-rgb: var(--moonstone-rgb);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    min-height: 100vh;
  }
}

/* Add Persian font support */
@font-face {
  font-family: "Vazirmatn";
  src: url("https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/fonts/webfonts/Vazirmatn-Regular.woff2")
    format("woff2");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Vazirmatn";
  src: url("https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/fonts/webfonts/Vazirmatn-Bold.woff2")
    format("woff2");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

body {
  font-family: "Vazirmatn", var(--font-geist-sans), system-ui, sans-serif;
}

/* Minimalistic Style Additions */
.card {
  border: 1px solid var(--border);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  border-radius: var(--radius);
}

.card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.button-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.input:focus,
.textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Ensure contrast in tabs */
button[class*="border-primary"] {
  border-width: 4px !important;
  border-bottom-width: 4px !important;
  font-weight: bold !important;
}

/* Minimalistic background utilities */
.bg-subtle {
  background-color: rgba(var(--platinum-rgb), 0.3);
}

.bg-accent-subtle {
  background-color: rgba(var(--powder-blue-rgb), 0.1);
}

.pattern-zigzag-secondary {
  background: linear-gradient(
        135deg,
        rgba(var(--secondary), 0.1) 25%,
        transparent 25%
      ) -10px 0,
    linear-gradient(225deg, rgba(var(--secondary), 0.1) 25%, transparent 25%) -10px
      0,
    linear-gradient(315deg, rgba(var(--secondary), 0.1) 25%, transparent 25%),
    linear-gradient(45deg, rgba(var(--secondary), 0.1) 25%, transparent 25%);
  background-size: 20px 20px;
}

.pattern-zigzag-lg {
  background: linear-gradient(
        135deg,
        rgba(var(--accent), 0.1) 25%,
        transparent 25%
      ) -15px 0,
    linear-gradient(225deg, rgba(var(--accent), 0.1) 25%, transparent 25%) -15px
      0,
    linear-gradient(315deg, rgba(var(--accent), 0.1) 25%, transparent 25%),
    linear-gradient(45deg, rgba(var(--accent), 0.1) 25%, transparent 25%);
  background-size: 30px 30px;
}

.pattern-zigzag-primary-lg {
  background: linear-gradient(
        135deg,
        rgba(var(--primary), 0.1) 25%,
        transparent 25%
      ) -15px 0,
    linear-gradient(225deg, rgba(var(--primary), 0.1) 25%, transparent 25%) -15px
      0,
    linear-gradient(315deg, rgba(var(--primary), 0.1) 25%, transparent 25%),
    linear-gradient(45deg, rgba(var(--primary), 0.1) 25%, transparent 25%);
  background-size: 30px 30px;
}

.pattern-zigzag-secondary-lg {
  background: linear-gradient(
        135deg,
        rgba(var(--secondary), 0.1) 25%,
        transparent 25%
      ) -15px 0,
    linear-gradient(225deg, rgba(var(--secondary), 0.1) 25%, transparent 25%) -15px
      0,
    linear-gradient(315deg, rgba(var(--secondary), 0.1) 25%, transparent 25%),
    linear-gradient(45deg, rgba(var(--secondary), 0.1) 25%, transparent 25%);
  background-size: 30px 30px;
}

.pattern-diamonds {
  background: linear-gradient(
        45deg,
        rgba(var(--chart-1), 0.08) 25%,
        transparent 25%
      ) -5px 0,
    linear-gradient(135deg, rgba(var(--chart-1), 0.08) 25%, transparent 25%) -5px
      0,
    linear-gradient(225deg, rgba(var(--chart-1), 0.08) 25%, transparent 25%),
    linear-gradient(315deg, rgba(var(--chart-1), 0.08) 25%, transparent 25%);
  background-size: 10px 10px;
  background-position: 0 0, 10px 0, 10px -10px, 0px 10px;
}

.pattern-crosses {
  background: radial-gradient(
      circle,
      transparent 20%,
      var(--background) 20%,
      var(--background) 80%,
      transparent 80%,
      transparent
    ),
    radial-gradient(
        circle,
        transparent 20%,
        var(--background) 20%,
        var(--background) 80%,
        transparent 80%,
        transparent
      )
      25px 25px,
    linear-gradient(rgba(var(--accent), 0.15) 2px, transparent 2px) 0 -1px,
    linear-gradient(90deg, rgba(var(--accent), 0.15) 2px, var(--background) 2px) -1px
      0;
  background-size: 50px 50px, 50px 50px, 25px 25px, 25px 25px;
}

.pattern-crosses-primary {
  background: radial-gradient(
      circle,
      transparent 20%,
      var(--background) 20%,
      var(--background) 80%,
      transparent 80%,
      transparent
    ),
    radial-gradient(
        circle,
        transparent 20%,
        var(--background) 20%,
        var(--background) 80%,
        transparent 80%,
        transparent
      )
      25px 25px,
    linear-gradient(rgba(var(--primary), 0.15) 2px, transparent 2px) 0 -1px,
    linear-gradient(
        90deg,
        rgba(var(--primary), 0.15) 2px,
        var(--background) 2px
      ) -1px 0;
  background-size: 50px 50px, 50px 50px, 25px 25px, 25px 25px;
}

.pattern-waves {
  background-image: repeating-radial-gradient(
      circle at 0 0,
      transparent 0,
      var(--background) 10px
    ),
    repeating-linear-gradient(
      rgba(var(--chart-5), 0.1),
      rgba(var(--chart-5), 0.1) 10px,
      rgba(var(--chart-3), 0.08) 10px,
      rgba(var(--chart-3), 0.08) 20px
    );
}

/* Fun animations for interactive elements - only for selected elements */
.fun-hover {
  transition: all 0.2s ease;
}

.fun-hover.rotate:hover {
  transform: rotate(2deg) scale(1.05);
}

.card {
  position: relative;
  overflow: hidden;
}

.card.card-shine::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
  pointer-events: none;
}

.card.card-shine:hover::after {
  left: 100%;
}

/* Navbar modifications */
.md\:fixed.md\:top-0.md\:left-0.md\:right-0.md\:z-50.md\:flex.md\:h-18.md\:items-center.md\:justify-between.md\:border-b-3.md\:border-border.md\:bg-background.md\:px-5.md\:py-3.lg\:px-8 {
  position: sticky !important;
  top: 0 !important;
  background-color: rgba(var(--background-rgb), 0.9) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  z-index: 50 !important;
  border-bottom: 2px solid var(--border) !important;
}

/* Mobile navbar enhancements */
.fixed.bottom-0.left-0.right-0.z-50.flex.h-18.items-center.justify-around.border-t-3.border-border.bg-background.py-3.md\:hidden {
  background-color: rgba(var(--background-rgb), 0.9) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-top: 2px solid var(--border) !important;
}

@keyframes text-shift {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Enhanced card effects for minimalistic design */
.card {
  transition: all 0.2s ease-in-out !important;
}

.card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.1) !important;
}

/* Global content margin adjustment for navbar */
main {
  margin-bottom: 5rem;
  padding-top: 1rem;
  animation: page-fade-in 0.3s ease-out;
}

/* Enhanced page transitions */
@keyframes page-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auth page specific enhancements - minimalistic */
.auth-form-field {
  position: relative;
  transition: all 0.2s ease-in-out;
}

.auth-form-field:hover {
  transform: translateY(-1px);
}

/* Enhanced loading spinner */
@keyframes auth-spin {
  0% {
    transform: rotate(0deg);
    border-color: currentColor transparent transparent transparent;
  }
  25% {
    border-color: transparent currentColor transparent transparent;
  }
  50% {
    border-color: transparent transparent currentColor transparent;
  }
  75% {
    border-color: transparent transparent transparent currentColor;
  }
  100% {
    transform: rotate(360deg);
    border-color: currentColor transparent transparent transparent;
  }
}

.auth-spinner {
  animation: auth-spin 1s linear infinite;
}

/* Enhanced toast styling for auth */
.auth-toast {
  font-family: "Vazirmatn", var(--font-geist-sans), system-ui, sans-serif !important;
  font-weight: bold !important;
  border-radius: 8px !important;
  padding: 16px 20px !important;
  font-size: 14px !important;
}

/* Form validation message styling */
.auth-error-message {
  background: rgba(var(--destructive-rgb), 0.1);
  border: 2px solid rgba(var(--destructive-rgb), 0.3);
  border-radius: 6px;
  padding: 8px 12px;
  margin-top: 8px;
  font-weight: 600;
  font-size: 13px;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Enhanced button hover effects for auth */
.auth-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.auth-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
  pointer-events: none;
}

.auth-button:hover::before {
  left: 100%;
}

.auth-button:active {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 var(--border) !important;
}
