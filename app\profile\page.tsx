"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import {
  Settings,
  Grid,
  Bookmark,
  Heart,
  LayoutGrid,
  PlusCircle,
} from "lucide-react";
import { MasonryGrid } from "@/components/ui/MasonryGrid";
import { FASHION_IMAGES } from "@/lib/constants";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import websocketService, {
  WebSocketEvent,
  ProfileData,
} from "@/lib/services/websocketService";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, Globe, Clock, Pencil, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import { EmptyState } from "@/components/ui/empty-state";

// Sample images for demo
const userImages = FASHION_IMAGES.slice(0, 10);
const savedImages = FASHION_IMAGES.slice(10, 15);
const likedImages = FASHION_IMAGES.slice(15, 20);

interface ProfileData {
  id: string;
  name: string;
  username: string;
  bio?: string;
  website?: string;
  profileImage?: string;
  profile_photo?: string;
  first_name?: string;
  last_name?: string;
  followers: number;
  following: number;
  pins?: Pin[];
  saved?: Pin[];
  pinCount: number;
  savedCount: number;
  isCurrentUser: boolean;
  currentUserId: string;
}

interface Pin {
  id: string;
  title: string;
  image: string;
  user: {
    id: string;
    name: string;
    username: string;
    profileImage?: string;
  };
  savedBy?: string[];
}

export default function ProfilePage() {
  const { isAuthenticated, loading, userData } = useAuthContext();
  const router = useRouter();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const wsRequestAttempted = useRef<boolean>(false);

  const [activeTab, setActiveTab] = useState("posts");
  const [isLoading, setIsLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !isAuthenticated) {
      console.log("User not authenticated, redirecting to auth page");
      toast.error("برای مشاهده پروفایل باید وارد حساب کاربری خود شوید");
      router.push("/auth");
    }
  }, [isAuthenticated, loading, router]);

  // Handle WebSocket connection and profile data
  useEffect(() => {
    if (!isAuthenticated) return;

    // Track if profile request already sent to prevent duplicates
    const profileRequestSent = { current: false };
    let isProfileDataReceived = false;

    // Handle profile data updates from any source
    const handleProfileData = (data: unknown) => {
      let userData: ProfileData | null = null;

      // Handle direct profile update
      if (typeof data === "object" && data !== null) {
        // From get_profile response
        if ("user" in data && "success" in data && data.success === true) {
          userData = (data as { user: ProfileData }).user;
        } else {
          // From profile_updated event
          userData = data as ProfileData;
        }
      }

      if (userData) {
        isProfileDataReceived = true;
        setProfileData(userData);
        setIsLoading(false);
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      }
    };

    const handleConnect = () => {
      // Only send the profile request if it hasn't been sent yet
      if (!profileRequestSent.current && !isProfileDataReceived) {
        profileRequestSent.current = true;
        wsRequestAttempted.current = true;
        websocketService.requestProfileData();
      }
    };

    // Setup event listeners
    websocketService.on(WebSocketEvent.CONNECTED, handleConnect);
    websocketService.on(WebSocketEvent.PROFILE_UPDATED, handleProfileData);
    websocketService.on("get_profile", handleProfileData);

    // Request profile data if connected and hasn't been requested yet
    if (
      websocketService.isConnected() &&
      !profileRequestSent.current &&
      !isProfileDataReceived
    ) {
      profileRequestSent.current = true;
      wsRequestAttempted.current = true;
      websocketService.requestProfileData();
    } else if (!websocketService.isConnected()) {
      websocketService.connect();
    }

    // Setup loading timeout with shorter duration
    timeoutRef.current = setTimeout(() => {
      if (!isProfileDataReceived) {
        setProfileData(userData as ProfileData);
      }
      setIsLoading(false);
    }, 5000);

    // Cleanup
    return () => {
      websocketService.off(WebSocketEvent.CONNECTED, handleConnect);
      websocketService.off(WebSocketEvent.PROFILE_UPDATED, handleProfileData);
      websocketService.off("get_profile", handleProfileData);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isAuthenticated, userData]);

  // Loading state
  if (loading || isLoading) {
    return (
      <div className="container mx-auto px-4 py-6 md:py-8 flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-moonstone border-t-transparent"></div>
          <p className="mt-4 text-lg">در حال بارگذاری پروفایل...</p>
          {isLoading && (
            <button
              onClick={() => {
                console.log("Manual skip loading, using local auth data");
                setProfileData(
                  (userData as ProfileData) || ({} as ProfileData)
                );
                setIsLoading(false);
              }}
              className="mt-4 text-sm text-moonstone hover:underline"
            >
              در صورت عدم بارگذاری، اینجا کلیک کنید
            </button>
          )}
        </div>
      </div>
    );
  }

  // Helper functions for UI
  const getBioText = () => String(profileData?.bio || "");

  const getProfilePhotoUrl = () => {
    if (!profileData?.profile_photo) return "/images/1.jpg";

    // Add cache-busting query parameter to prevent browser caching
    const cacheBuster = `?v=${Date.now()}`;

    return profileData.profile_photo.startsWith("/media")
      ? `${process.env.NEXT_PUBLIC_API_BASE || ""}${
          profileData.profile_photo
        }${cacheBuster}`
      : `${profileData.profile_photo}${cacheBuster}`;
  };

  const getUsername = () =>
    profileData?.username || userData?.username || "کاربر";

  // Get images based on active tab
  const getActiveImages = () => {
    switch (activeTab) {
      case "saved":
        return savedImages;
      case "liked":
        return likedImages;
      default:
        return userImages;
    }
  };

  const activeImages = getActiveImages();

  const formatWebsiteUrl = (website: string | undefined) => {
    if (!website) return "";

    let url = website;
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "https://" + url;
    }

    // For display purposes, remove protocol and trailing slash
    const displayUrl = url.replace(/^https?:\/\//, "").replace(/\/$/, "");

    return {
      url,
      displayUrl,
    };
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("fa-IR", {
      year: "numeric",
      month: "long",
    });
  };

  return (
    <div className="min-h-screen">
      <div className="container py-5 px-2 md:px-4 lg:px-6 xl:px-8 max-w-7xl mx-auto">
        {/* Profile Header */}
        <div className="mb-10 rounded-xl border border-platinum bg-white p-4 shadow-sm md:p-6">
          <div className="flex flex-col items-center md:flex-row md:items-start md:gap-8">
            <Avatar className="h-32 w-32 border border-platinum shadow-md sm:h-40 sm:w-40">
              <AvatarImage src={getProfilePhotoUrl()} alt={getUsername()} />
              <AvatarFallback className="text-4xl font-extrabold">
                {getUsername().slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="mt-4 flex flex-1 flex-col items-center text-center md:items-start md:text-start">
              <div className="flex flex-wrap items-center justify-center gap-2 md:justify-start">
                <h1 className="text-3xl font-black">@{getUsername()}</h1>
                {userData?.isVerified && (
                  <Badge
                    variant="outline"
                    className="border-moonstone bg-moonstone/10 text-moonstone font-medium"
                  >
                    <span className="ml-1">تایید شده</span>
                    <CheckCircle2 className="h-4 w-4" />
                  </Badge>
                )}
              </div>
              {userData?.first_name && userData?.last_name && (
                <p className="text-lg font-semibold text-foreground mt-1">
                  {userData.first_name} {userData.last_name}
                </p>
              )}
              <p className="mt-1 text-muted-foreground max-w-3xl">
                {getBioText() || "این کاربر هنوز بیوگرافی ثبت نکرده است."}
              </p>
              <div className="mt-2 flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                {userData?.website && (
                  <div className="flex items-center">
                    <Link
                      href={formatWebsiteUrl(userData.website).url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 hover:text-moonstone transition-colors"
                    >
                      <Globe className="h-4 w-4" />
                      <span>
                        {formatWebsiteUrl(userData.website).displayUrl}
                      </span>
                    </Link>
                  </div>
                )}

                <div className="flex items-center">
                  <Clock className="h-4 w-4" />
                  <span className="mr-1">
                    عضویت: {formatDate(userData?.createdAt)}
                  </span>
                </div>
              </div>
              <div className="mt-4 flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-1">
                  <span className="text-lg font-black">
                    {userData?.followersCount}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    دنبال‌کننده
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-lg font-black">
                    {userData?.followingCount}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    دنبال‌شده
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-lg font-black">
                    {userData?.pinCount}
                  </span>
                  <span className="text-sm text-muted-foreground">پین</span>
                </div>
              </div>
            </div>
            <div className="mt-6 md:mt-0">
              {userData?.isCurrentUser ? (
                <Link href="/profile/edit">
                  <Button className="font-bold button-hover">
                    <Pencil className="ml-2 h-4 w-4 rtl:ml-0 rtl:mr-2" />
                    <span>ویرایش پروفایل</span>
                  </Button>
                </Link>
              ) : (
                <FollowButton
                  userId={userData.id}
                  isFollowing={userData.isFollowing}
                />
              )}
            </div>
          </div>
        </div>

        {/* Profile Tabs */}
        <div className="space-y-4">
          <div className="flex overflow-x-auto rounded-lg border border-platinum bg-white shadow-sm">
            <TabButton
              active={activeTab === "pins"}
              onClick={() => setActiveTab("pins")}
              icon={<Grid className="h-5 w-5" />}
              label="پین‌ها"
              count={userData?.pinCount || 0}
            />
            <TabButton
              active={activeTab === "saved"}
              onClick={() => setActiveTab("saved")}
              icon={<Bookmark className="h-5 w-5" />}
              label="ذخیره‌شده‌ها"
              count={userData?.savedCount || 0}
            />
          </div>

          <div className="min-h-[300px]">
            {activeTab === "pins" ? (
              userData?.pinCount > 0 ? (
                <MasonryGrid
                  pins={userData.pins || []}
                  currentUserId={userData.currentUserId || ""}
                />
              ) : (
                <EmptyState
                  icon={<LayoutGrid className="h-10 w-10" />}
                  title="هنوز پینی وجود ندارد"
                  description={
                    userData?.isCurrentUser
                      ? "شما هنوز هیچ پینی ایجاد نکرده‌اید. با ایجاد پین، تصاویر مد را با دیگران به اشتراک بگذارید."
                      : "این کاربر هنوز هیچ پینی ایجاد نکرده است."
                  }
                  action={
                    userData?.isCurrentUser && (
                      <Link href="/create">
                        <Button className="button-hover font-bold">
                          <PlusCircle className="ml-2 h-5 w-5 rtl:ml-0 rtl:mr-2" />
                          <span>ایجاد پین جدید</span>
                        </Button>
                      </Link>
                    )
                  }
                />
              )
            ) : userData?.savedCount > 0 ? (
              <MasonryGrid
                pins={userData.saved || []}
                currentUserId={userData.currentUserId || ""}
              />
            ) : (
              <EmptyState
                icon={<Bookmark className="h-10 w-10" />}
                title="هنوز پین ذخیره شده‌ای وجود ندارد"
                description={
                  userData?.isCurrentUser
                    ? "شما هنوز هیچ پینی را ذخیره نکرده‌اید. پین‌های مورد علاقه خود را ذخیره کنید تا بعداً به آن‌ها دسترسی داشته باشید."
                    : "این کاربر هنوز هیچ پینی را ذخیره نکرده است."
                }
                action={
                  userData?.isCurrentUser && (
                    <Link href="/">
                      <Button className="button-hover font-bold">
                        <Home className="ml-2 h-5 w-5 rtl:ml-0 rtl:mr-2" />
                        <span>مشاهده پین‌های جدید</span>
                      </Button>
                    </Link>
                  )
                }
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function TabButton({
  active,
  onClick,
  icon,
  label,
  count,
}: {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  count: number;
}) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-1 items-center justify-center gap-2 px-4 py-3 font-bold transition-colors md:flex-none md:px-6",
        active
          ? "bg-moonstone/10 text-moonstone border-b-2 border-moonstone"
          : "text-muted-foreground hover:bg-powder-blue/10 hover:text-powder-blue"
      )}
    >
      {icon}
      <span>{label}</span>
      <span
        className={cn(
          "rounded-full px-2 text-xs",
          active
            ? "bg-moonstone/20 text-moonstone"
            : "bg-platinum text-muted-foreground"
        )}
      >
        {count}
      </span>
    </button>
  );
}

function FollowButton({
  userId,
  isFollowing,
}: {
  userId: string;
  isFollowing: boolean;
}) {
  const router = useRouter();
  const [following, setFollowing] = useState(isFollowing);
  const [loading, setLoading] = useState(false);

  const toggleFollow = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));
      setFollowing(!following);
      toast.success(
        following
          ? "کاربر از لیست دنبال شدگان حذف شد"
          : "کاربر با موفقیت دنبال شد"
      );
    } catch (error) {
      toast.error("خطا در انجام عملیات");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={toggleFollow}
      variant={following ? "outline" : "default"}
      className={cn(
        "font-bold",
        following
          ? "hover:bg-melon/10 hover:text-melon border-melon/30"
          : "bg-moonstone hover:bg-moonstone/90 border-moonstone text-white shadow-sm hover:shadow-md transition-all"
      )}
      disabled={loading}
    >
      {loading ? (
        <span>در حال پردازش...</span>
      ) : following ? (
        <span>دنبال شده</span>
      ) : (
        <span>دنبال کردن</span>
      )}
    </Button>
  );
}
