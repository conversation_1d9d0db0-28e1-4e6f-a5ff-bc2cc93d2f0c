import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      dir="rtl"
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-10 w-full min-w-0 rounded-lg border border-border bg-background px-4 py-2 text-base outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-sm transition-all duration-200",
        "focus:border-primary focus:shadow-md focus:ring-2 focus:ring-primary/20",
        "hover:shadow-[5px_5px_0_0] hover:shadow-border hover:-translate-y-0.5 hover:-translate-x-0.5",
        "aria-invalid:border-destructive aria-invalid:shadow-destructive",
        "dark:bg-background/40 dark:focus:bg-background/60",
        className
      )}
      {...props}
    />
  );
}

export { Input };
