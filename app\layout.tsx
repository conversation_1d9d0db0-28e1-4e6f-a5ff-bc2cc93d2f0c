import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { MobileNavbar, DesktopNavbar } from "@/components/layout/Navbar";
import { WEBSITE_NAME } from "@/lib/constants";

import { Toaster } from "@/components/ui/sonner";
import AuthProvider from "@/lib/providers/AuthProvider";
import Image from "next/image";
import Link from "next/link";
import logo from "@/public/temp_logo.png";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: WEBSITE_NAME,
  description: "پلتفرم اشتراک گذاری مد برای الهام گرفتن از استایل",
};

export default function RootLayout({
  children,
}: <PERSON>only<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" dir="rtl" suppressHydrationWarning className="h-full">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full bg-white`}
      >
        <AuthProvider>
          <div className="flex flex-col min-h-screen">
            <DesktopNavbar />
            {/* Mobile Top Header with Logo */}
            <div className="flex h-16 items-center justify-center bg-white border-b border-platinum px-4 py-2 md:hidden">
              <Link href="/" className="flex items-center gap-3">
                <Image
                  src={logo}
                  alt="logo"
                  className="h-12 w-14 object-contain rounded-lg"
                />
                <span className="text-xl font-bold text-charcoal">
                  {WEBSITE_NAME}
                </span>
              </Link>
            </div>
            <main className="flex-1 pb-16 md:pb-0">{children}</main>
            <MobileNavbar />
          </div>
          <Toaster richColors position="top-center" />
        </AuthProvider>
      </body>
    </html>
  );
}
