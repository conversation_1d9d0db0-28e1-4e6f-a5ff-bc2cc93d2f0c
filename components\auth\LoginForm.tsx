"use client";

import { UseFormReturn } from "react-hook-form";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Form, FormField } from "@/components/ui/form";
import { AuthFormField } from "./AuthFormField";
import { AuthButton } from "./AuthButton";
import { User, Lock, HelpCircle, LogIn, RotateCcw } from "lucide-react";

interface LoginFormData {
  identifier: string;
  password: string;
}

interface LoginFormProps {
  form: UseFormReturn<LoginFormData>;
  onSubmit: (data: LoginFormData) => void;
  isLoading: boolean;
}

export function LoginForm({ form, onSubmit, isLoading }: LoginFormProps) {
  const router = useRouter();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="identifier"
          render={({ field }) => (
            <AuthFormField
              label="نام کاربری / ایمیل"
              icon={<User className="h-5 w-5" />}
              placeholder="نام کاربری یا ایمیل خود را وارد کنید"
              field={field}
              disabled={isLoading}
            />
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <AuthFormField
              label="رمز عبور"
              icon={<Lock className="h-5 w-5" />}
              placeholder="رمز عبور خود را وارد کنید"
              type="password"
              field={field}
              disabled={isLoading}
            >
              <div className="flex justify-end mt-2">
                <Link
                  href="/auth/reset-password"
                  className="text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200 underline decoration-2 underline-offset-2 flex items-center gap-2"
                >
                  <HelpCircle className="h-4 w-4" />
                  <span>فراموشی رمز عبور؟</span>
                </Link>
              </div>
            </AuthFormField>
          )}
        />

        <AuthButton
          type="submit"
          variant="primary"
          isLoading={isLoading}
          loadingText="در حال ورود..."
        >
          <div className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            <span>ورود</span>
          </div>
        </AuthButton>

        <div className="relative flex items-center justify-center mt-8 mb-6">
          <div className="absolute w-full border-t-3 border-dashed border-border"></div>
          <div className="relative bg-card px-4 text-sm font-bold text-muted-foreground">
            یا
          </div>
        </div>

        <AuthButton
          type="button"
          variant="outline"
          onClick={() => router.push("/auth/reset-password")}
          disabled={isLoading}
        >
          <div className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            <span>بازیابی رمز عبور</span>
          </div>
        </AuthButton>
      </form>
    </Form>
  );
}
