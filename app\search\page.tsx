"use client";

import { useState } from "react";
import { AiOutlineSearch } from "react-icons/ai";
import { MasonryGrid } from "@/components/ui/MasonryGrid";
import { FASHION_IMAGES } from "@/lib/constants";

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("");

  // In a real app, we'd filter images based on tags, descriptions, etc.
  // For demo purposes, we're just showing all images
  const filteredImages = FASHION_IMAGES;

  return (
    <div className="container mx-auto px-4 py-6 md:py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">جستجوی استایل‌ها</h1>
        <div className="relative">
          <input
            type="text"
            placeholder="جستجو برای استایل‌ها، سبک‌ها یا کاربران..."
            className="w-full p-3 pr-10 border border-platinum rounded-lg bg-white rtl:pl-3 focus:border-moonstone focus:ring-2 focus:ring-moonstone/20 transition-all"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <AiOutlineSearch
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
            size={20}
          />
        </div>
      </div>

      <MasonryGrid images={filteredImages} />
    </div>
  );
}
