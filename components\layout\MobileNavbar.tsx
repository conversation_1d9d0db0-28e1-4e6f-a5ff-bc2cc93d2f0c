"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { HomeIcon, Search, PlusSquare, Heart, User } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuthContext } from "@/lib/providers/AuthProvider";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const MobileNavbar = () => {
  const pathname = usePathname();
  const { isAuthenticated, userData } = useAuthContext();

  // Format user avatar URL
  const avatarUrl = userData?.profile_photo
    ? `${process.env.NEXT_PUBLIC_API_BASE || ""}${
        userData.profile_photo
      }?v=${Date.now()}`
    : "/images/1.jpg";

  // Get user initials for avatar fallback
  const userInitials = userData?.username
    ? userData.username.slice(0, 2).toUpperCase()
    : userData?.first_name && userData.last_name
    ? `${userData.first_name[0]}${userData.last_name[0]}`.toUpperCase()
    : "U";

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 border-t border-border bg-background p-2 z-10">
      <div className="flex items-center justify-around">
        <NavItem
          href="/"
          icon={<HomeIcon size={24} />}
          active={pathname === "/"}
        />
        <NavItem
          href="/search"
          icon={<Search size={24} />}
          active={pathname === "/search"}
        />
        {isAuthenticated && (
          <NavItem
            href="/upload"
            icon={<PlusSquare size={24} />}
            active={pathname === "/upload"}
          />
        )}
        <NavItem
          href="/explore"
          icon={<Heart size={24} />}
          active={pathname === "/explore"}
        />

        {isAuthenticated ? (
          <Link
            href="/profile"
            className={cn(
              "flex flex-col items-center justify-center p-2 rounded-lg",
              pathname === "/profile" && "text-primary"
            )}
          >
            <Avatar className="h-6 w-6">
              <AvatarImage src={avatarUrl} alt="پروفایل" />
              <AvatarFallback>{userInitials}</AvatarFallback>
            </Avatar>
            <span className="text-xs mt-1">پروفایل</span>
          </Link>
        ) : (
          <Link
            href="/auth"
            className="flex flex-col items-center justify-center p-2 rounded-lg"
          >
            <User size={24} />
            <span className="text-xs mt-1">ورود</span>
          </Link>
        )}
      </div>
    </div>
  );
};

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  active?: boolean;
}

const NavItem = ({ href, icon, active }: NavItemProps) => {
  return (
    <Link
      href={href}
      className={cn(
        "flex flex-col items-center justify-center p-2 rounded-lg",
        active && "text-primary"
      )}
    >
      {icon}
    </Link>
  );
};

export default MobileNavbar;
