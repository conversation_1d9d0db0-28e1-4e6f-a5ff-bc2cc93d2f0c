import * as React from "react";

import { cn } from "@/lib/utils";

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        "border-input placeholder:text-muted-foreground flex field-sizing-content min-h-16 w-full rounded-md border-2 border-border bg-transparent px-3 py-2 text-base outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow-[3px_3px_0_0] shadow-border transition-all",
        "focus:border-primary focus:shadow-primary focus:shadow-[3px_3px_0_0] focus:-translate-y-0.5 focus:-translate-x-0.5",
        "aria-invalid:border-destructive aria-invalid:shadow-destructive",
        className
      )}
      {...props}
    />
  );
}

export { Textarea };
