"use client";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import websocketService, {
  WebSocketEvent,
  ProfileData,
} from "@/lib/services/websocketService";
import { toast } from "sonner";
import { X } from "lucide-react";

// اعتبارسنجی فرم با zod
const profileFormSchema = z.object({
  username: z
    .string()
    .min(3, { message: "نام کاربری باید حداقل ۳ کاراکتر باشد." }),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  bio: z.string().optional(),
});

export default function EditProfilePage() {
  const router = useRouter();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadedPhoto, setUploadedPhoto] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const wsInitialized = useRef(false);

  // تنظیم فرم با مقادیر پیش‌فرض
  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: "",
      first_name: "",
      last_name: "",
      bio: "",
    },
  });

  // دریافت اطلاعات پروفایل از وب‌سوکت
  useEffect(() => {
    if (!wsInitialized.current) {
      wsInitialized.current = true;

      // Handle profile data from get_profile or profile_updated events
      const handleProfileData = (data: unknown) => {
        const profile = data as ProfileData;

        if (profile) {
          // Update form with the received profile data
          form.reset({
            username: profile.username || "",
            first_name: profile.first_name || "",
            last_name: profile.last_name || "",
            bio: profile.bio || "",
          });

          // Set profile photo if available
          if (profile.profile_photo) {
            // Add cache busting parameter to prevent browser caching
            const cacheBuster = `?v=${Date.now()}`;
            const photoUrl = profile.profile_photo.startsWith("/media")
              ? `${process.env.NEXT_PUBLIC_API_BASE || ""}${
                  profile.profile_photo
                }${cacheBuster}`
              : `${profile.profile_photo}${cacheBuster}`;

            setPreviewUrl(photoUrl);
          }

          setIsLoading(false);
        }
      };

      // Set up event listeners for both profile update and get_profile responses
      websocketService.on(WebSocketEvent.PROFILE_UPDATED, handleProfileData);
      websocketService.on("get_profile", (data: unknown) => {
        if (typeof data === "object" && data !== null && "user" in data) {
          handleProfileData((data as { user: ProfileData }).user);
        }
      });

      // Request profile data if connected
      if (websocketService.isConnected()) {
        websocketService.requestProfileData();
      } else {
        // Connect and request data when connected
        websocketService.on(WebSocketEvent.CONNECTED, () => {
          websocketService.requestProfileData();
        });
        websocketService.connect();
      }
    }

    return () => {
      // Clean up event listeners
      websocketService.off(WebSocketEvent.PROFILE_UPDATED, () => {});
      websocketService.off("get_profile", () => {});
      websocketService.off(WebSocketEvent.CONNECTED, () => {});
    };
  }, [form]);

  // تغییر عکس پروفایل
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        setPreviewUrl(base64);
        setUploadedPhoto(base64.split(",")[1]); // ذخیره فقط داده base64 بدون پیشوند
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = (data: z.infer<typeof profileFormSchema>) => {
    setIsSubmitting(true);

    // Prepare the message payload for profile update
    const message = {
      job: "update_profile",
      first_name: data.first_name || "",
      last_name: data.last_name || "",
      bio: data.bio || "",
      ...(uploadedPhoto ? { photo: uploadedPhoto } : {}),
    };

    // Attach a one-time listener to wait for the update_profile response
    websocketService.once(WebSocketEvent.MESSAGE, (msg: unknown) => {
      setIsSubmitting(false);
      const response = msg as {
        job: string;
        success: boolean;
        [key: string]: unknown;
      };

      if (response.job === "update_profile") {
        if (response.success) {
          toast.success("پروفایل به‌روزرسانی شد.");
          router.push("/profile");
        } else {
          toast.error("خطا در به‌روزرسانی پروفایل.");
        }
      }
    });

    // Send the update_profile message
    websocketService.sendMessage(message);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="container mx-auto flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-lg">در حال بارگذاری اطلاعات پروفایل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-2xl mx-auto px-4 py-6">
      <Card>
        <CardHeader>
          <CardTitle>ویرایش پروفایل</CardTitle>
          <CardDescription>
            اطلاعات پروفایل خود را به‌روزرسانی کنید
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* بخش تغییر عکس پروفایل */}
              <div className="flex flex-col items-center">
                <div className="relative">
                  <Avatar className="w-24 h-24">
                    {previewUrl ? (
                      <AvatarImage
                        src={previewUrl}
                        alt="عکس پروفایل"
                        onError={() => {
                          // If image fails to load, clear the URL and show fallback
                          console.error("Failed to load profile image");
                          setPreviewUrl(null);
                        }}
                      />
                    ) : (
                      <AvatarFallback>
                        {form.getValues().username.slice(0, 2).toUpperCase() ||
                          "کاربر"}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  {previewUrl && (
                    <button
                      type="button"
                      className="absolute -top-1 -right-1 rounded-full bg-red-100 text-red-500 p-1"
                      onClick={() => setPreviewUrl(null)}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    id="avatar-upload"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                  <label htmlFor="avatar-upload">
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-2"
                      onClick={() =>
                        document.getElementById("avatar-upload")?.click()
                      }
                    >
                      تغییر عکس
                    </Button>
                  </label>
                </div>
              </div>

              {/* اطلاعات پایه پروفایل */}
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>نام کاربری</FormLabel>
                    <FormControl>
                      <Input placeholder="نام کاربری" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>نام</FormLabel>
                    <FormControl>
                      <Input placeholder="نام" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>نام خانوادگی</FormLabel>
                    <FormControl>
                      <Input placeholder="نام خانوادگی" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>بیوگرافی</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="بیوگرافی خود را وارد کنید..."
                        className="min-h-[100px] resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/profile")}
                  disabled={isSubmitting}
                >
                  انصراف
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <span className="mr-2">در حال ذخیره...</span>
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-r-transparent" />
                    </>
                  ) : (
                    "ذخیره تغییرات"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
