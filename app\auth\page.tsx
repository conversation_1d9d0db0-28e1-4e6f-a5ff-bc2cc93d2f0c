"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { motion, AnimatePresence } from "framer-motion";

import { useAuthContext } from "@/lib/providers/AuthProvider";
import authService from "@/lib/services/authService";
import { authToast } from "@/lib/utils/authToast";
import { Lock, Hand, PartyPopper, Plus, ArrowLeft } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Auth Components
import { CustomTabs } from "@/components/auth/CustomTabs";
import { LoginForm } from "@/components/auth/LoginForm";
import { RegisterForm } from "@/components/auth/RegisterForm";
import { VerificationForm } from "@/components/auth/VerificationForm";
import { AuthButton } from "@/components/auth/AuthButton";

// Validation schemas
const loginSchema = z.object({
  identifier: z.string().min(1, { message: "نام کاربری یا ایمیل الزامی است" }),
  password: z
    .string()
    .min(8, { message: "رمز عبور باید حداقل ۸ کاراکتر باشد" }),
});

const registerSchema = z
  .object({
    username: z
      .string()
      .min(3, { message: "نام کاربری باید حداقل ۳ کاراکتر باشد" })
      .max(30, { message: "نام کاربری نباید بیش از ۳۰ کاراکتر باشد" })
      .regex(/^[a-zA-Z0-9_]+$/, {
        message: "نام کاربری فقط می‌تواند شامل حروف انگلیسی، اعداد و _ باشد",
      }),
    email: z
      .string()
      .min(1, { message: "ایمیل الزامی است" })
      .email({ message: "فرمت ایمیل نامعتبر است" }),
    password: z
      .string()
      .min(8, { message: "رمز عبور باید حداقل ۸ کاراکتر باشد" })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
        message:
          "رمز عبور باید شامل حداقل یک حرف کوچک، یک حرف بزرگ و یک عدد باشد",
      }),
    confirm_password: z
      .string()
      .min(8, { message: "تکرار رمز عبور باید حداقل ۸ کاراکتر باشد" }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "رمز عبور و تکرار آن باید یکسان باشند",
    path: ["confirm_password"],
  });

const verifyCodeSchema = z.object({
  code: z
    .string()
    .min(1, { message: "کد تایید الزامی است" })
    .refine((val) => !isNaN(Number(val)), {
      message: "کد تایید باید عدد باشد",
    }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type VerifyCodeFormValues = z.infer<typeof verifyCodeSchema>;

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState<string>("login");
  const [isLoading, setIsLoading] = useState(false);
  const [needsVerification, setNeedsVerification] = useState(false);
  const router = useRouter();
  const { isAuthenticated, hasTempTokens, setAuthTokens, checkAuth } =
    useAuthContext();
  const [isToastShown, setIsToastShown] = useState(false);

  // Redirect if the user is already authenticated
  useEffect(() => {
    // Check if user has temporary tokens (registered but not verified)
    if (hasTempTokens) {
      setNeedsVerification(true);
    }

    // Only redirect to profile if user is fully authenticated
    else if (isAuthenticated) {
      router.push("/profile");
    }
  }, [router, isAuthenticated, hasTempTokens]);

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      identifier: "",
      password: "",
    },
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirm_password: "",
    },
  });

  const verifyCodeForm = useForm<VerifyCodeFormValues>({
    resolver: zodResolver(verifyCodeSchema),
    defaultValues: {
      code: "",
    },
  });

  async function onLoginSubmit(data: LoginFormValues) {
    setIsLoading(true);

    // Prevent multiple toasts
    if (isToastShown) return;

    try {
      const response = await authService.login(data.identifier, data.password);

      if (response.success && response.access_token && response.refresh_token) {
        // Update auth context with tokens and user data
        setAuthTokens(
          response.access_token,
          response.refresh_token,
          response.user_data
        );

        setIsToastShown(true);
        authToast.success("🎉 ورود با موفقیت انجام شد!");

        // Small delay before redirect to ensure auth state is updated
        setTimeout(() => {
          router.push("/profile");
        }, 300);
      } else {
        authToast.error("❌ نام کاربری یا رمز عبور اشتباه است");
      }
    } catch (error) {
      console.error("Login error:", error);
      authToast.error("🚫 خطا در ورود به سیستم. لطفاً دوباره تلاش کنید");
    } finally {
      setIsLoading(false);
      // Reset toast flag after a delay
      setTimeout(() => {
        setIsToastShown(false);
      }, 3000);
    }
  }

  async function onRegisterSubmit(data: RegisterFormValues) {
    setIsLoading(true);

    // Prevent multiple toasts
    if (isToastShown) return;

    try {
      const response = await authService.register(
        data.email,
        data.username,
        data.password,
        data.confirm_password
      );

      if (response.success) {
        setIsToastShown(true);

        authToast.success(
          "🎉 ثبت‌نام با موفقیت انجام شد! لطفا کد تایید را وارد کنید."
        );

        setNeedsVerification(true);
        setActiveTab("login");
        // Auth state will be updated by the useEffect
        checkAuth();
      } else {
        authToast.error("❌ خطا در ثبت‌نام. لطفاً اطلاعات را بررسی کنید");
      }
    } catch (error) {
      console.error("Register error:", error);
      authToast.error("🚫 خطا در ثبت‌نام. لطفاً دوباره تلاش کنید");
    } finally {
      setIsLoading(false);
      // Reset toast flag after a delay
      setTimeout(() => {
        setIsToastShown(false);
      }, 3000);
    }
  }

  async function onVerifyCodeSubmit(data: VerifyCodeFormValues) {
    setIsLoading(true);

    // Prevent multiple toasts
    if (isToastShown) return;

    try {
      // The verifyCode function now handles getting the access token from storage
      const response = await authService.verifyCode(Number(data.code));

      if (response.success) {
        setIsToastShown(true);

        // Force refresh authentication state to ensure it's updated
        checkAuth();

        authToast.success("✅ حساب کاربری شما با موفقیت تایید شد!");

        // Add a small delay before redirect to ensure auth state is updated
        setTimeout(() => {
          router.push("/profile");
        }, 300);
      } else {
        authToast.error("❌ کد تایید نامعتبر است. لطفاً دوباره تلاش کنید");
      }
    } catch (error) {
      console.error("Verification error:", error);
      authToast.error("🚫 خطا در تایید کد. لطفاً دوباره تلاش کنید");
    } finally {
      setIsLoading(false);
      // Reset toast flag after a delay
      setTimeout(() => {
        setIsToastShown(false);
      }, 3000);
    }
  }

  async function handleResendCode() {
    setIsLoading(true);

    try {
      const response = await authService.resendVerifyCode();

      if (response.success) {
        authToast.info("📧 کد تایید مجدداً ارسال شد");
      } else {
        authToast.error("❌ خطا در ارسال مجدد کد تایید");
      }
    } catch (error) {
      console.error("Resend code error:", error);
      authToast.error("🚫 خطا در ارسال مجدد کد تایید");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex flex-col py-8 md:py-12 items-center justify-center ">
      <div className="w-full md:max-w-md px-4 md:px-6 mx-auto">
        <Card className="border border-platinum shadow-lg overflow-hidden py-0 bg-white">
          <CardHeader className="pb-2 bg-white border-b border-platinum">
            <CardTitle className="text-xl md:text-2xl lg:text-3xl font-bold text-center mb-2 py-4 text-charcoal">
              <div className="flex items-center justify-center gap-3">
                {needsVerification ? (
                  <>
                    <Lock className="h-6 w-6 md:h-7 md:w-7" />
                    <span>تایید حساب کاربری</span>
                  </>
                ) : activeTab === "login" ? (
                  <>
                    <Hand className="h-6 w-6 md:h-7 md:w-7" />
                    <span>ورود به حساب کاربری</span>
                  </>
                ) : (
                  <>
                    <PartyPopper className="h-6 w-6 md:h-7 md:w-7" />
                    <span>ثبت‌نام</span>
                  </>
                )}
              </div>
            </CardTitle>
            <CardDescription className="text-center text-sm md:text-base font-medium text-muted-foreground">
              {needsVerification
                ? "کد تایید ارسال شده به ایمیل خود را وارد کنید"
                : activeTab === "login"
                ? "برای ورود ایمیل و رمز عبور خود را وارد کنید"
                : "برای ثبت‌نام اطلاعات زیر را وارد کنید"}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6 px-6 bg-transparent">
            {needsVerification ? (
              <VerificationForm
                form={verifyCodeForm}
                onSubmit={onVerifyCodeSubmit}
                onResendCode={handleResendCode}
                onBackToLogin={() => {
                  setNeedsVerification(false);
                  setActiveTab("login");
                  loginForm.reset();
                  registerForm.reset();
                  verifyCodeForm.reset();
                }}
                isLoading={isLoading}
              />
            ) : (
              <div className="w-full">
                <CustomTabs
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                  isLoading={isLoading}
                />

                <AnimatePresence mode="wait">
                  {activeTab === "login" ? (
                    <motion.div
                      key="login"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.15 }}
                    >
                      <LoginForm
                        form={loginForm}
                        onSubmit={onLoginSubmit}
                        isLoading={isLoading}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="register"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.15 }}
                    >
                      <RegisterForm
                        form={registerForm}
                        onSubmit={onRegisterSubmit}
                        isLoading={isLoading}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </CardContent>
          {!needsVerification && (
            <CardFooter className="flex justify-center bg-transparent border-t border-platinum pt-6 pb-6">
              <AuthButton
                variant="ghost"
                onClick={() => {
                  setActiveTab(activeTab === "login" ? "register" : "login");
                  loginForm.reset();
                  registerForm.reset();
                }}
                disabled={isLoading}
                className="text-sm font-medium text-charcoal hover:text-moonstone hover:underline transition-all duration-200 decoration-2 underline-offset-4"
              >
                <div className="flex items-center gap-2">
                  {activeTab === "login" ? (
                    <>
                      <Plus className="h-4 w-4" />
                      <span>حساب کاربری ندارید؟ ثبت‌نام کنید</span>
                    </>
                  ) : (
                    <>
                      <ArrowLeft className="h-4 w-4" />
                      <span>قبلاً ثبت‌نام کرده‌اید؟ وارد شوید</span>
                    </>
                  )}
                </div>
              </AuthButton>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}
