"use client";

import { ReactNode } from "react";
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface AuthFormFieldProps {
  label: string;
  icon: ReactNode;
  placeholder: string;
  type?: string;
  disabled?: boolean;
  field: any;
  children?: ReactNode;
}

export function AuthFormField({
  label,
  icon,
  placeholder,
  type = "text",
  disabled = false,
  field,
  children,
}: AuthFormFieldProps) {
  return (
    <FormItem className="space-y-3">
      <FormLabel className="font-medium text-base text-charcoal flex items-center gap-2">
        {icon} {label}
      </FormLabel>
      <FormControl>
        <Input
          type={type}
          placeholder={placeholder}
          {...field}
          disabled={disabled}
          className="text-base font-normal h-11 border-platinum focus:border-moonstone focus:ring-moonstone/20"
        />
      </FormControl>
      <FormMessage className="text-melon font-medium" />
      {children}
    </FormItem>
  );
}
