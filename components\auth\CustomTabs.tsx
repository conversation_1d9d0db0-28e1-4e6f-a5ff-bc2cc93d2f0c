"use client";

import { LogIn, UserPlus } from "lucide-react";

interface CustomTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isLoading: boolean;
}

export function CustomTabs({
  activeTab,
  onTabChange,
  isLoading,
}: CustomTabsProps) {
  return (
    <div className="w-full mb-8">
      <div className="grid grid-cols-2 gap-1 p-1 border border-platinum rounded-lg shadow-sm bg-platinum/20">
        {/* Register tab - positioned on the right */}
        <button
          type="button"
          onClick={() => onTabChange("register")}
          disabled={isLoading}
          className={`
            relative h-11 px-4 font-medium text-base rounded-lg border transition-all duration-200
            ${
              activeTab === "register"
                ? "bg-white text-charcoal border-platinum shadow-sm"
                : "bg-transparent text-charcoal/70 border-transparent hover:bg-white/50 hover:text-charcoal"
            }
            disabled:opacity-50 disabled:cursor-not-allowed
          `}
        >
          <div className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            <span>ثبت‌نام</span>
          </div>
        </button>

        {/* Login tab - positioned on the left */}
        <button
          type="button"
          onClick={() => onTabChange("login")}
          disabled={isLoading}
          className={`
            relative h-11 px-4 font-medium text-base rounded-lg border transition-all duration-200
            ${
              activeTab === "login"
                ? "bg-white text-charcoal border-platinum shadow-sm"
                : "bg-transparent text-charcoal/70 border-transparent hover:bg-white/50 hover:text-charcoal"
            }
            disabled:opacity-50 disabled:cursor-not-allowed
          `}
        >
          <div className="flex items-center gap-2">
            <LogIn className="h-4 w-4 rotate-180" />
            <span>ورود</span>
          </div>
        </button>
      </div>
    </div>
  );
}
